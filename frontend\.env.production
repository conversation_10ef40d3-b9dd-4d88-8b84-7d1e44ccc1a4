# 生产环境配置文件

# 应用基础配置
REACT_APP_ENV=production
REACT_APP_VERSION=1.0.0
REACT_APP_BUILD_TIME=__BUILD_TIME__

# API配置
REACT_APP_API_BASE_URL=https://api.bookstore.com
REACT_APP_API_TIMEOUT=10000
REACT_APP_API_RETRY_TIMES=3

# CDN配置
REACT_APP_CDN_BASE_URL=https://cdn.bookstore.com
REACT_APP_STATIC_URL=https://static.bookstore.com

# 第三方服务配置
REACT_APP_ANALYTICS_ID=GA-XXXXXXXXX
REACT_APP_SENTRY_DSN=https://<EMAIL>/xxxxxxx
REACT_APP_HOTJAR_ID=xxxxxxx

# 支付配置
REACT_APP_ALIPAY_APP_ID=xxxxxxxxx
REACT_APP_WECHAT_APP_ID=xxxxxxxxx

# 地图服务配置
REACT_APP_AMAP_KEY=xxxxxxxxx
REACT_APP_BAIDU_MAP_KEY=xxxxxxxxx

# 文件上传配置
REACT_APP_UPLOAD_MAX_SIZE=10485760
REACT_APP_UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp

# 缓存配置
REACT_APP_CACHE_VERSION=1.0.0
REACT_APP_CACHE_DURATION=3600000

# 功能开关
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_ERROR_TRACKING=true
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true

# 安全配置
REACT_APP_ENABLE_CSP=true
REACT_APP_ENABLE_HTTPS_ONLY=true

# 调试配置
REACT_APP_DEBUG_MODE=false
REACT_APP_SHOW_PERFORMANCE_METRICS=false

# 构建优化
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false
IMAGE_INLINE_SIZE_LIMIT=0
