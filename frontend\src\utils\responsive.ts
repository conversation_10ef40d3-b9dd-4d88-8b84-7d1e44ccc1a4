import { theme } from '../styles/theme';

// 响应式断点
export const breakpoints = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600
};

// 媒体查询生成器
export const mediaQuery = {
  // 最大宽度查询
  down: (breakpoint: keyof typeof breakpoints) => 
    `@media (max-width: ${breakpoints[breakpoint] - 1}px)`,
  
  // 最小宽度查询
  up: (breakpoint: keyof typeof breakpoints) => 
    `@media (min-width: ${breakpoints[breakpoint]}px)`,
  
  // 范围查询
  between: (min: keyof typeof breakpoints, max: keyof typeof breakpoints) => 
    `@media (min-width: ${breakpoints[min]}px) and (max-width: ${breakpoints[max] - 1}px)`,
  
  // 仅特定断点
  only: (breakpoint: keyof typeof breakpoints) => {
    const keys = Object.keys(breakpoints) as (keyof typeof breakpoints)[];
    const index = keys.indexOf(breakpoint);
    
    if (index === 0) {
      return `@media (max-width: ${breakpoints[keys[1]] - 1}px)`;
    } else if (index === keys.length - 1) {
      return `@media (min-width: ${breakpoints[breakpoint]}px)`;
    } else {
      return `@media (min-width: ${breakpoints[breakpoint]}px) and (max-width: ${breakpoints[keys[index + 1]] - 1}px)`;
    }
  }
};

// 响应式值生成器
export const responsive = {
  // 字体大小
  fontSize: {
    xs: {
      h1: '1.5rem',
      h2: '1.25rem',
      h3: '1.125rem',
      h4: '1rem',
      body: '0.875rem',
      small: '0.75rem'
    },
    sm: {
      h1: '1.75rem',
      h2: '1.5rem',
      h3: '1.25rem',
      h4: '1.125rem',
      body: '0.875rem',
      small: '0.75rem'
    },
    md: {
      h1: '2rem',
      h2: '1.75rem',
      h3: '1.5rem',
      h4: '1.25rem',
      body: '1rem',
      small: '0.875rem'
    },
    lg: {
      h1: '2.5rem',
      h2: '2rem',
      h3: '1.75rem',
      h4: '1.5rem',
      body: '1rem',
      small: '0.875rem'
    },
    xl: {
      h1: '3rem',
      h2: '2.5rem',
      h3: '2rem',
      h4: '1.75rem',
      body: '1.125rem',
      small: '1rem'
    }
  },
  
  // 间距
  spacing: {
    xs: {
      container: '16px',
      section: '24px',
      element: '8px'
    },
    sm: {
      container: '20px',
      section: '32px',
      element: '12px'
    },
    md: {
      container: '24px',
      section: '48px',
      element: '16px'
    },
    lg: {
      container: '32px',
      section: '64px',
      element: '20px'
    },
    xl: {
      container: '40px',
      section: '80px',
      element: '24px'
    }
  },
  
  // 网格列数
  columns: {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
    xxl: 6
  }
};

// 响应式容器样式生成器
export const createResponsiveContainer = (maxWidth?: string) => `
  width: 100%;
  margin: 0 auto;
  padding: 0 ${responsive.spacing.xs.container};
  
  ${mediaQuery.up('sm')} {
    padding: 0 ${responsive.spacing.sm.container};
  }
  
  ${mediaQuery.up('md')} {
    padding: 0 ${responsive.spacing.md.container};
  }
  
  ${mediaQuery.up('lg')} {
    padding: 0 ${responsive.spacing.lg.container};
    max-width: ${maxWidth || '1200px'};
  }
  
  ${mediaQuery.up('xl')} {
    padding: 0 ${responsive.spacing.xl.container};
  }
`;

// 响应式网格样式生成器
export const createResponsiveGrid = (
  gap: string = '16px',
  minItemWidth: string = '280px'
) => `
  display: grid;
  gap: ${gap};
  grid-template-columns: repeat(auto-fit, minmax(${minItemWidth}, 1fr));
  
  ${mediaQuery.down('sm')} {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  ${mediaQuery.between('sm', 'md')} {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  ${mediaQuery.between('md', 'lg')} {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  
  ${mediaQuery.up('lg')} {
    gap: ${gap};
  }
`;

// 响应式文字样式生成器
export const createResponsiveText = (
  baseSize: string = '1rem',
  scaleFactor: number = 1.2
) => `
  font-size: ${baseSize};
  
  ${mediaQuery.down('sm')} {
    font-size: calc(${baseSize} * 0.875);
  }
  
  ${mediaQuery.up('lg')} {
    font-size: calc(${baseSize} * ${scaleFactor});
  }
  
  ${mediaQuery.up('xl')} {
    font-size: calc(${baseSize} * ${scaleFactor * 1.1});
  }
`;

// 响应式间距生成器
export const createResponsiveSpacing = (
  property: 'margin' | 'padding',
  direction?: 'top' | 'right' | 'bottom' | 'left' | 'x' | 'y'
) => {
  const getProperty = (dir?: string) => {
    if (!dir) return property;
    if (dir === 'x') return `${property}-left, ${property}-right`;
    if (dir === 'y') return `${property}-top, ${property}-bottom`;
    return `${property}-${dir}`;
  };

  const prop = getProperty(direction);

  return `
    ${prop}: ${responsive.spacing.xs.element};
    
    ${mediaQuery.up('sm')} {
      ${prop}: ${responsive.spacing.sm.element};
    }
    
    ${mediaQuery.up('md')} {
      ${prop}: ${responsive.spacing.md.element};
    }
    
    ${mediaQuery.up('lg')} {
      ${prop}: ${responsive.spacing.lg.element};
    }
    
    ${mediaQuery.up('xl')} {
      ${prop}: ${responsive.spacing.xl.element};
    }
  `;
};

// 隐藏/显示工具类
export const visibility = {
  hideOn: (breakpoint: keyof typeof breakpoints) => `
    ${mediaQuery.down(breakpoint)} {
      display: none !important;
    }
  `,
  
  showOn: (breakpoint: keyof typeof breakpoints) => `
    display: none !important;
    
    ${mediaQuery.up(breakpoint)} {
      display: block !important;
    }
  `,
  
  onlyOn: (breakpoint: keyof typeof breakpoints) => `
    display: none !important;
    
    ${mediaQuery.only(breakpoint)} {
      display: block !important;
    }
  `
};

// 响应式Flex布局
export const createResponsiveFlex = (
  direction: 'row' | 'column' = 'row',
  mobileDirection: 'row' | 'column' = 'column'
) => `
  display: flex;
  flex-direction: ${mobileDirection};
  gap: ${responsive.spacing.xs.element};
  
  ${mediaQuery.up('md')} {
    flex-direction: ${direction};
    gap: ${responsive.spacing.md.element};
  }
`;

// 导出所有工具
export default {
  breakpoints,
  mediaQuery,
  responsive,
  createResponsiveContainer,
  createResponsiveGrid,
  createResponsiveText,
  createResponsiveSpacing,
  visibility,
  createResponsiveFlex
};
