import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { theme } from './styles/theme';

// 全局测试提供者
const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <ConfigProvider locale={zhCN}>
          {children}
        </ConfigProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

// 自定义渲染函数
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// 模拟数据
export const mockBook = {
  id: '1',
  title: '测试图书',
  author: '测试作者',
  publisher: '测试出版社',
  price: 29.99,
  originalPrice: 39.99,
  description: '这是一本测试图书的描述',
  category: 'fiction',
  condition: 'excellent',
  stock: 10,
  images: ['/test-image.jpg'],
  rating: 4.5,
  reviewCount: 100,
  isbn: '9787111111111',
  publishDate: '2023-01-01',
  tags: ['小说', '文学'],
  seller: {
    id: '1',
    name: '测试卖家',
    avatar: '/test-avatar.jpg',
    rating: 4.8
  }
};

export const mockUser = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  avatar: '/test-avatar.jpg',
  nickname: '测试用户',
  phone: '13800138000',
  address: '测试地址',
  createdAt: '2023-01-01',
  isVerified: true
};

export const mockCartItem = {
  id: '1',
  bookId: '1',
  book: mockBook,
  quantity: 2,
  selected: true,
  addedAt: '2023-01-01'
};

export const mockOrder = {
  id: '1',
  userId: '1',
  items: [mockCartItem],
  totalAmount: 59.98,
  status: 'pending',
  shippingAddress: {
    name: '测试用户',
    phone: '13800138000',
    address: '测试地址',
    city: '测试城市',
    province: '测试省份',
    zipCode: '100000'
  },
  createdAt: '2023-01-01',
  updatedAt: '2023-01-01'
};

// 模拟API响应
export const mockApiResponse = {
  success: true,
  data: null,
  message: '操作成功',
  code: 200
};

// 模拟错误响应
export const mockErrorResponse = {
  success: false,
  data: null,
  message: '操作失败',
  code: 500
};

// 模拟分页响应
export const mockPaginatedResponse = {
  success: true,
  data: {
    items: [],
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 0
  },
  message: '获取成功',
  code: 200
};

// 测试工具函数
export const createMockFunction = () => jest.fn();

export const createMockPromise = <T>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay);
  });
};

export const createMockRejectedPromise = (error: Error, delay = 0) => {
  return new Promise((_, reject) => {
    setTimeout(() => reject(error), delay);
  });
};

// 模拟本地存储
export const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// 模拟会话存储
export const mockSessionStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// 模拟窗口对象
export const mockWindow = {
  location: {
    href: 'http://localhost:3000',
    pathname: '/',
    search: '',
    hash: '',
    reload: jest.fn(),
  },
  history: {
    pushState: jest.fn(),
    replaceState: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
  },
  scrollTo: jest.fn(),
  alert: jest.fn(),
  confirm: jest.fn(),
  prompt: jest.fn(),
};

// 模拟媒体查询
export const mockMediaQuery = (query: string) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(),
  removeListener: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
});

// 模拟IntersectionObserver
export const mockIntersectionObserver = {
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
};

// 模拟ResizeObserver
export const mockResizeObserver = {
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
};

// 设置全局模拟
export const setupGlobalMocks = () => {
  // 模拟localStorage
  Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage,
    writable: true,
  });

  // 模拟sessionStorage
  Object.defineProperty(window, 'sessionStorage', {
    value: mockSessionStorage,
    writable: true,
  });

  // 模拟matchMedia
  Object.defineProperty(window, 'matchMedia', {
    value: jest.fn().mockImplementation(mockMediaQuery),
    writable: true,
  });

  // 模拟IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => mockIntersectionObserver);

  // 模拟ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => mockResizeObserver);

  // 模拟scrollTo
  window.scrollTo = jest.fn();

  // 模拟console方法（避免测试时的日志输出）
  global.console = {
    ...console,
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
};

// 清理模拟
export const cleanupMocks = () => {
  jest.clearAllMocks();
  mockLocalStorage.getItem.mockClear();
  mockLocalStorage.setItem.mockClear();
  mockLocalStorage.removeItem.mockClear();
  mockLocalStorage.clear.mockClear();
  
  mockSessionStorage.getItem.mockClear();
  mockSessionStorage.setItem.mockClear();
  mockSessionStorage.removeItem.mockClear();
  mockSessionStorage.clear.mockClear();
};

// 重新导出所有testing-library的工具
export * from '@testing-library/react';
export { customRender as render };
