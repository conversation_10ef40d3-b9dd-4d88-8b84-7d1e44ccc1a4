import React, { useState } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { 
  SearchOutlined, 
  ShoppingCartOutlined, 
  UserOutlined, 
  MenuOutlined,
  BookOutlined
} from '@ant-design/icons';
import { theme } from '../../../styles/theme';
import { Container, Flex } from '../../../styles/globalStyles';
import Button from '../../ui/Button';
import Input from '../../ui/Input';

// Header容器
const HeaderContainer = styled.header`
  background-color: ${theme.colors.white};
  border-bottom: 1px solid ${theme.colors.border};
  box-shadow: ${theme.boxShadow.sm};
  position: sticky;
  top: 0;
  z-index: 1000;
`;

// Logo样式
const Logo = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  font-size: ${theme.typography.fontSize.xl};
  font-weight: ${theme.typography.fontWeight.bold};
  color: ${theme.colors.primary};
  text-decoration: none;
  
  &:hover {
    color: ${theme.colors.primary[300]};
  }
`;

// 搜索框容器
const SearchContainer = styled.div`
  flex: 1;
  max-width: 500px;
  margin: 0 ${theme.spacing.lg};
  
  ${theme.mediaQueries.md} {
    margin: 0 ${theme.spacing.xl};
  }
`;

// 导航菜单
const NavMenu = styled.nav<{ isOpen?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.lg};
  
  ${theme.mediaQueries.md} {
    display: ${props => props.isOpen ? 'flex' : 'none'};
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: ${theme.colors.white};
    border-bottom: 1px solid ${theme.colors.border};
    padding: ${theme.spacing.md};
    flex-direction: column;
    align-items: stretch;
    gap: ${theme.spacing.md};
  }
  
  ${theme.mediaQueries.lg} {
    display: flex;
    position: static;
    background: none;
    border: none;
    padding: 0;
    flex-direction: row;
    align-items: center;
    gap: ${theme.spacing.lg};
  }
`;

// 导航链接
const NavLink = styled(Link)`
  color: ${theme.colors.textPrimary};
  font-weight: ${theme.typography.fontWeight.medium};
  padding: ${theme.spacing.sm} 0;
  border-bottom: 2px solid transparent;
  transition: all ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};
  
  &:hover {
    color: ${theme.colors.primary};
    border-bottom-color: ${theme.colors.primary};
  }
`;

// 用户操作区域
const UserActions = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.md};
`;

// 购物车图标容器
const CartIconContainer = styled.div`
  position: relative;
  cursor: pointer;
  padding: ${theme.spacing.sm};
  border-radius: ${theme.borderRadius.base};
  transition: background-color ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};
  
  &:hover {
    background-color: ${theme.colors.gray[100]};
  }
`;

// 购物车徽章
const CartBadge = styled.span`
  position: absolute;
  top: 0;
  right: 0;
  background-color: ${theme.colors.error};
  color: ${theme.colors.white};
  font-size: ${theme.typography.fontSize.xs};
  font-weight: ${theme.typography.fontWeight.bold};
  padding: 2px 6px;
  border-radius: ${theme.borderRadius.full};
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(50%, -50%);
`;

// 移动端菜单按钮
const MobileMenuButton = styled.button`
  display: none;
  padding: ${theme.spacing.sm};
  border-radius: ${theme.borderRadius.base};
  transition: background-color ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};
  
  &:hover {
    background-color: ${theme.colors.gray[100]};
  }
  
  ${theme.mediaQueries.lg} {
    display: none !important;
  }
  
  @media (max-width: 991px) {
    display: block;
  }
`;

// Header组件属性接口
interface HeaderProps {
  cartItemCount?: number;
  isLoggedIn?: boolean;
  userName?: string;
  onSearch?: (query: string) => void;
  onCartClick?: () => void;
  onLoginClick?: () => void;
  onLogoutClick?: () => void;
}

// Header组件
export const Header: React.FC<HeaderProps> = ({
  cartItemCount = 0,
  isLoggedIn = false,
  userName,
  onSearch,
  onCartClick,
  onLoginClick,
  onLogoutClick
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <HeaderContainer>
      <Container>
        <Flex align="center" justify="space-between" style={{ height: '64px' }}>
          {/* Logo */}
          <Logo to="/">
            <BookOutlined />
            旧书买卖
          </Logo>

          {/* 搜索框 */}
          <SearchContainer>
            <form onSubmit={handleSearch}>
              <Input
                placeholder="搜索图书、作者、ISBN..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                suffix={
                  <SearchOutlined 
                    style={{ cursor: 'pointer' }} 
                    onClick={handleSearch}
                  />
                }
                fullWidth
              />
            </form>
          </SearchContainer>

          {/* 桌面端导航 */}
          <NavMenu isOpen={isMenuOpen}>
            <NavLink to="/books">图书分类</NavLink>
            <NavLink to="/about">关于我们</NavLink>
            <NavLink to="/contact">联系我们</NavLink>
          </NavMenu>

          {/* 用户操作区域 */}
          <UserActions>
            {/* 购物车 */}
            <CartIconContainer onClick={onCartClick}>
              <ShoppingCartOutlined style={{ fontSize: '20px' }} />
              {cartItemCount > 0 && (
                <CartBadge>{cartItemCount > 99 ? '99+' : cartItemCount}</CartBadge>
              )}
            </CartIconContainer>

            {/* 用户登录/注册 */}
            {isLoggedIn ? (
              <Flex align="center" gap={theme.spacing.sm}>
                <UserOutlined />
                <span>{userName}</span>
                <Button variant="ghost" size="small" onClick={onLogoutClick}>
                  退出
                </Button>
              </Flex>
            ) : (
              <Flex gap={theme.spacing.sm}>
                <Button variant="ghost" size="small" onClick={onLoginClick}>
                  登录
                </Button>
                <Button variant="primary" size="small">
                  注册
                </Button>
              </Flex>
            )}

            {/* 移动端菜单按钮 */}
            <MobileMenuButton onClick={toggleMenu}>
              <MenuOutlined />
            </MobileMenuButton>
          </UserActions>
        </Flex>
      </Container>
    </HeaderContainer>
  );
};

export default Header;
