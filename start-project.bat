@echo off
echo ========================================
echo 收书卖书平台 - 项目启动脚本
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 正在检查npm环境...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到npm，请重新安装Node.js
    pause
    exit /b 1
)

echo.
echo Node.js和npm环境检查完成！
echo.

echo 正在进入前端目录...
cd /d "%~dp0frontend"

echo 检查依赖是否已安装...
if not exist "node_modules" (
    echo 依赖未安装，正在安装依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
    echo 依赖安装完成！
) else (
    echo 依赖已存在，跳过安装步骤
)

echo.
echo ========================================
echo 正在启动开发服务器...
echo ========================================
echo.
echo 服务器启动后将自动打开浏览器
echo 如果没有自动打开，请手动访问: http://localhost:3000
echo.
echo 按 Ctrl+C 可以停止服务器
echo.

npm start

echo.
echo 服务器已停止
pause
