import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Slider,
  Button,
  Space,
  Divider,
  Typography,
  Row,
  Col,
  Checkbox,
  DatePicker,
  Tag
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  BookOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { categoriesService } from '../../services/categories';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const SearchContainer = styled.div`
  .search-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    
    &:hover {
      border-color: #1677ff;
      box-shadow: 0 2px 8px rgba(22, 119, 255, 0.2);
    }
    
    .search-icon {
      color: #8c8c8c;
    }
    
    .search-text {
      color: #8c8c8c;
      flex: 1;
    }
    
    .filter-icon {
      color: #1677ff;
    }
  }
  
  .search-modal {
    .ant-modal-content {
      border-radius: 12px;
    }
    
    .search-form {
      .form-section {
        margin-bottom: 24px;
        
        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .section-content {
          background: #fafafa;
          padding: 16px;
          border-radius: 8px;
        }
      }
      
      .price-range {
        .ant-slider {
          margin: 16px 0;
        }
        
        .price-inputs {
          display: flex;
          gap: 8px;
          align-items: center;
          margin-top: 12px;
          
          .price-input {
            flex: 1;
          }
        }
      }
      
      .condition-options {
        .ant-checkbox-group {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .ant-checkbox-wrapper {
            margin: 0;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            transition: all 0.3s ease;
            
            &:hover {
              border-color: #1677ff;
            }
            
            &.ant-checkbox-wrapper-checked {
              background: #e6f4ff;
              border-color: #1677ff;
            }
          }
        }
      }
      
      .quick-filters {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px;
        
        .quick-filter {
          padding: 4px 12px;
          border: 1px solid #d9d9d9;
          border-radius: 16px;
          background: white;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 12px;
          
          &:hover {
            border-color: #1677ff;
            color: #1677ff;
          }
          
          &.active {
            background: #1677ff;
            border-color: #1677ff;
            color: white;
          }
        }
      }
    }
    
    .search-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24px;
      
      .action-left {
        .clear-btn {
          color: #8c8c8c;
        }
      }
      
      .action-right {
        display: flex;
        gap: 8px;
      }
    }
  }
`;

interface SearchFilters {
  keyword?: string;
  category_id?: string;
  author?: string;
  publisher?: string;
  min_price?: number;
  max_price?: number;
  condition?: string[];
  sort?: string;
  has_discount?: boolean;
  in_stock?: boolean;
  date_range?: [string, string];
}

interface AdvancedSearchProps {
  onSearch: (filters: SearchFilters) => void;
  initialFilters?: SearchFilters;
  className?: string;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  onSearch,
  initialFilters = {},
  className
}) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [categories, setCategories] = useState<any[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [activeQuickFilters, setActiveQuickFilters] = useState<string[]>([]);

  // 快速筛选选项
  const quickFilters = [
    { key: 'new_arrivals', label: '新品上架', filters: { sort: 'created_at_DESC' } },
    { key: 'hot_sales', label: '热销图书', filters: { sort: 'sales_count_DESC' } },
    { key: 'low_price', label: '低价优选', filters: { max_price: 50 } },
    { key: 'high_rating', label: '高评分', filters: { sort: 'rating_DESC' } },
    { key: 'discounted', label: '特价促销', filters: { has_discount: true } },
    { key: 'in_stock', label: '现货', filters: { in_stock: true } }
  ];

  // 图书状况选项
  const conditionOptions = [
    { label: '全新', value: '全新' },
    { label: '九成新', value: '九成新' },
    { label: '八成新', value: '八成新' },
    { label: '七成新', value: '七成新' },
    { label: '六成新', value: '六成新' }
  ];

  // 排序选项
  const sortOptions = [
    { label: '综合排序', value: 'relevance' },
    { label: '价格从低到高', value: 'price_ASC' },
    { label: '价格从高到低', value: 'price_DESC' },
    { label: '销量从高到低', value: 'sales_count_DESC' },
    { label: '最新发布', value: 'created_at_DESC' },
    { label: '浏览量从高到低', value: 'views_DESC' }
  ];

  useEffect(() => {
    loadCategories();
    form.setFieldsValue(initialFilters);
  }, []);

  const loadCategories = async () => {
    try {
      const response = await categoriesService.getCategories();
      if (response.success && response.data) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const handleQuickFilter = (filterKey: string) => {
    const filter = quickFilters.find(f => f.key === filterKey);
    if (!filter) return;

    const isActive = activeQuickFilters.includes(filterKey);
    
    if (isActive) {
      // 移除筛选
      setActiveQuickFilters(prev => prev.filter(key => key !== filterKey));
      // 清除对应的表单字段
      const currentValues = form.getFieldsValue();
      Object.keys(filter.filters).forEach(key => {
        delete currentValues[key];
      });
      form.setFieldsValue(currentValues);
    } else {
      // 添加筛选
      setActiveQuickFilters(prev => [...prev, filterKey]);
      form.setFieldsValue({
        ...form.getFieldsValue(),
        ...filter.filters
      });
    }
  };

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const filters: SearchFilters = {
      ...values,
      min_price: priceRange[0],
      max_price: priceRange[1]
    };

    // 清理空值
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof SearchFilters] === undefined || 
          filters[key as keyof SearchFilters] === '' ||
          (Array.isArray(filters[key as keyof SearchFilters]) && 
           (filters[key as keyof SearchFilters] as any[]).length === 0)) {
        delete filters[key as keyof SearchFilters];
      }
    });

    onSearch(filters);
    setVisible(false);
  };

  const handleClear = () => {
    form.resetFields();
    setPriceRange([0, 1000]);
    setActiveQuickFilters([]);
  };

  const getSearchSummary = () => {
    const values = form.getFieldsValue();
    const parts = [];
    
    if (values.keyword) parts.push(`"${values.keyword}"`);
    if (values.category_id) {
      const category = categories.find(c => c.id === values.category_id);
      if (category) parts.push(category.name);
    }
    if (values.author) parts.push(`作者: ${values.author}`);
    if (priceRange[0] > 0 || priceRange[1] < 1000) {
      parts.push(`¥${priceRange[0]}-${priceRange[1]}`);
    }
    if (activeQuickFilters.length > 0) {
      const labels = activeQuickFilters.map(key => 
        quickFilters.find(f => f.key === key)?.label
      ).filter(Boolean);
      parts.push(...labels);
    }

    return parts.length > 0 ? parts.join(' · ') : '点击搜索图书';
  };

  return (
    <SearchContainer className={className}>
      <div className="search-trigger" onClick={() => setVisible(true)}>
        <SearchOutlined className="search-icon" />
        <span className="search-text">{getSearchSummary()}</span>
        <FilterOutlined className="filter-icon" />
      </div>

      <Modal
        title="高级搜索"
        open={visible}
        onCancel={() => setVisible(false)}
        footer={null}
        width={800}
        className="search-modal"
      >
        <Form form={form} layout="vertical" className="search-form">
          {/* 快速筛选 */}
          <div className="form-section">
            <div className="section-title">
              <BookOutlined />
              快速筛选
            </div>
            <div className="quick-filters">
              {quickFilters.map(filter => (
                <div
                  key={filter.key}
                  className={`quick-filter ${activeQuickFilters.includes(filter.key) ? 'active' : ''}`}
                  onClick={() => handleQuickFilter(filter.key)}
                >
                  {filter.label}
                </div>
              ))}
            </div>
          </div>

          <Row gutter={24}>
            {/* 基础搜索 */}
            <Col span={24}>
              <div className="form-section">
                <div className="section-title">基础信息</div>
                <div className="section-content">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="keyword" label="关键词">
                        <Input placeholder="图书名称、ISBN、关键词" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="category_id" label="分类">
                        <Select placeholder="选择分类" allowClear>
                          {categories.map(category => (
                            <Option key={category.id} value={category.id}>
                              {category.name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="author" label="作者">
                        <Input placeholder="作者姓名" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="publisher" label="出版社">
                        <Input placeholder="出版社名称" />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </div>
            </Col>

            {/* 价格范围 */}
            <Col span={12}>
              <div className="form-section">
                <div className="section-title">价格范围</div>
                <div className="section-content">
                  <div className="price-range">
                    <Slider
                      range
                      min={0}
                      max={1000}
                      value={priceRange}
                      onChange={(value) => setPriceRange(value as [number, number])}
                      marks={{
                        0: '¥0',
                        200: '¥200',
                        500: '¥500',
                        1000: '¥1000+'
                      }}
                    />
                    <div className="price-inputs">
                      <Input
                        className="price-input"
                        prefix="¥"
                        value={priceRange[0]}
                        onChange={(e) => setPriceRange([Number(e.target.value) || 0, priceRange[1]])}
                      />
                      <Text>至</Text>
                      <Input
                        className="price-input"
                        prefix="¥"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], Number(e.target.value) || 1000])}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Col>

            {/* 图书状况 */}
            <Col span={12}>
              <div className="form-section">
                <div className="section-title">图书状况</div>
                <div className="section-content">
                  <div className="condition-options">
                    <Form.Item name="condition">
                      <Checkbox.Group options={conditionOptions} />
                    </Form.Item>
                  </div>
                </div>
              </div>
            </Col>

            {/* 其他选项 */}
            <Col span={24}>
              <div className="form-section">
                <div className="section-title">其他选项</div>
                <div className="section-content">
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item name="sort" label="排序方式">
                        <Select placeholder="选择排序方式">
                          {sortOptions.map(option => (
                            <Option key={option.value} value={option.value}>
                              {option.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="has_discount" valuePropName="checked">
                        <Checkbox>仅显示特价图书</Checkbox>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="in_stock" valuePropName="checked">
                        <Checkbox>仅显示有库存</Checkbox>
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </div>
            </Col>
          </Row>
        </Form>

        <div className="search-actions">
          <div className="action-left">
            <Button 
              type="text" 
              icon={<ClearOutlined />}
              className="clear-btn"
              onClick={handleClear}
            >
              清空条件
            </Button>
          </div>
          <div className="action-right">
            <Button onClick={() => setVisible(false)}>
              取消
            </Button>
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              搜索
            </Button>
          </div>
        </div>
      </Modal>
    </SearchContainer>
  );
};

export default AdvancedSearch;
