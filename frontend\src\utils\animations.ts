import { keyframes, css } from 'styled-components';
import { theme } from '../styles/theme';

// 基础动画关键帧
export const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

export const fadeOut = keyframes`
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
`;

export const slideInUp = keyframes`
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

export const slideInDown = keyframes`
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

export const slideInLeft = keyframes`
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

export const slideInRight = keyframes`
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

export const scaleIn = keyframes`
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
`;

export const scaleOut = keyframes`
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.8);
    opacity: 0;
  }
`;

export const bounceIn = keyframes`
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

export const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

export const shake = keyframes`
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
`;

export const rotate = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

export const float = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
`;

export const glow = keyframes`
  0%, 100% {
    box-shadow: 0 0 5px ${theme.colors.primary[300]};
  }
  50% {
    box-shadow: 0 0 20px ${theme.colors.primary[400]}, 0 0 30px ${theme.colors.primary[300]};
  }
`;

// 动画组合器
export const animations = {
  // 淡入动画
  fadeIn: (duration: string = theme.animation.duration.base, delay: string = '0s') => css`
    animation: ${fadeIn} ${duration} ${theme.animation.easing.easeOut} ${delay} both;
  `,

  // 淡出动画
  fadeOut: (duration: string = theme.animation.duration.base, delay: string = '0s') => css`
    animation: ${fadeOut} ${duration} ${theme.animation.easing.easeOut} ${delay} both;
  `,

  // 滑入动画
  slideInUp: (duration: string = theme.animation.duration.base, delay: string = '0s') => css`
    animation: ${slideInUp} ${duration} ${theme.animation.easing.easeOut} ${delay} both;
  `,

  slideInDown: (duration: string = theme.animation.duration.base, delay: string = '0s') => css`
    animation: ${slideInDown} ${duration} ${theme.animation.easing.easeOut} ${delay} both;
  `,

  slideInLeft: (duration: string = theme.animation.duration.base, delay: string = '0s') => css`
    animation: ${slideInLeft} ${duration} ${theme.animation.easing.easeOut} ${delay} both;
  `,

  slideInRight: (duration: string = theme.animation.duration.base, delay: string = '0s') => css`
    animation: ${slideInRight} ${duration} ${theme.animation.easing.easeOut} ${delay} both;
  `,

  // 缩放动画
  scaleIn: (duration: string = theme.animation.duration.base, delay: string = '0s') => css`
    animation: ${scaleIn} ${duration} ${theme.animation.easing.easeOut} ${delay} both;
  `,

  scaleOut: (duration: string = theme.animation.duration.base, delay: string = '0s') => css`
    animation: ${scaleOut} ${duration} ${theme.animation.easing.easeOut} ${delay} both;
  `,

  // 弹跳动画
  bounceIn: (duration: string = theme.animation.duration.slow, delay: string = '0s') => css`
    animation: ${bounceIn} ${duration} ${theme.animation.easing.bounce} ${delay} both;
  `,

  // 脉冲动画
  pulse: (duration: string = '2s', delay: string = '0s') => css`
    animation: ${pulse} ${duration} ${theme.animation.easing.easeInOut} ${delay} infinite;
  `,

  // 摇摆动画
  shake: (duration: string = '0.5s', delay: string = '0s') => css`
    animation: ${shake} ${duration} ${theme.animation.easing.easeInOut} ${delay};
  `,

  // 旋转动画
  rotate: (duration: string = '1s', delay: string = '0s') => css`
    animation: ${rotate} ${duration} linear ${delay} infinite;
  `,

  // 浮动动画
  float: (duration: string = '3s', delay: string = '0s') => css`
    animation: ${float} ${duration} ${theme.animation.easing.easeInOut} ${delay} infinite;
  `,

  // 发光动画
  glow: (duration: string = '2s', delay: string = '0s') => css`
    animation: ${glow} ${duration} ${theme.animation.easing.easeInOut} ${delay} infinite;
  `
};

// 悬停效果
export const hoverEffects = {
  // 上升效果
  lift: css`
    transition: transform ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    
    &:hover {
      transform: translateY(-4px);
    }
  `,

  // 缩放效果
  scale: css`
    transition: transform ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    
    &:hover {
      transform: scale(1.05);
    }
  `,

  // 阴影效果
  shadow: css`
    transition: box-shadow ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    
    &:hover {
      box-shadow: ${theme.boxShadow.xl};
    }
  `,

  // 发光效果
  glow: css`
    transition: box-shadow ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    
    &:hover {
      box-shadow: 0 0 20px ${theme.colors.primary[300]};
    }
  `,

  // 旋转效果
  rotate: css`
    transition: transform ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    
    &:hover {
      transform: rotate(5deg);
    }
  `,

  // 倾斜效果
  skew: css`
    transition: transform ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    
    &:hover {
      transform: skew(-2deg);
    }
  `
};

// 点击效果
export const clickEffects = {
  // 按下效果
  press: css`
    transition: transform ${theme.animation.duration.fast} ${theme.animation.easing.easeOut};
    
    &:active {
      transform: scale(0.95);
    }
  `,

  // 波纹效果
  ripple: css`
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      transition: width 0.6s, height 0.6s;
    }
    
    &:active::before {
      width: 300px;
      height: 300px;
    }
  `
};

// 加载动画
export const loadingAnimations = {
  // 旋转加载
  spin: css`
    animation: ${rotate} 1s linear infinite;
  `,

  // 脉冲加载
  pulse: css`
    animation: ${pulse} 1.5s ${theme.animation.easing.easeInOut} infinite;
  `,

  // 浮动加载
  float: css`
    animation: ${float} 2s ${theme.animation.easing.easeInOut} infinite;
  `
};

// 页面转场动画
export const pageTransitions = {
  // 淡入淡出
  fade: css`
    &.page-enter {
      opacity: 0;
    }
    
    &.page-enter-active {
      opacity: 1;
      transition: opacity ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    }
    
    &.page-exit {
      opacity: 1;
    }
    
    &.page-exit-active {
      opacity: 0;
      transition: opacity ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    }
  `,

  // 滑动
  slide: css`
    &.page-enter {
      transform: translateX(100%);
    }
    
    &.page-enter-active {
      transform: translateX(0);
      transition: transform ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    }
    
    &.page-exit {
      transform: translateX(0);
    }
    
    &.page-exit-active {
      transform: translateX(-100%);
      transition: transform ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
    }
  `
};

// 导出所有动画工具
export default {
  keyframes: {
    fadeIn,
    fadeOut,
    slideInUp,
    slideInDown,
    slideInLeft,
    slideInRight,
    scaleIn,
    scaleOut,
    bounceIn,
    pulse,
    shake,
    rotate,
    float,
    glow
  },
  animations,
  hoverEffects,
  clickEffects,
  loadingAnimations,
  pageTransitions
};
