import React, { useState, useEffect } from 'react';
import { Card, Typography, Rate, Tag, Skeleton } from 'antd';
import { 
  BookOutlined, 
  HeartOutlined, 
  EyeOutlined,
  ShoppingCartOutlined,
  FireOutlined,
  StarOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { <PERSON><PERSON>, AnimatedWrapper } from '../ui';
import { theme } from '../../styles/theme';

const { Title, Text, Paragraph } = Typography;

interface Book {
  id: string;
  title: string;
  author: string;
  cover: string;
  price: number;
  originalPrice: number;
  rating: number;
  reviewCount: number;
  tags: string[];
  description: string;
  isHot?: boolean;
  isNew?: boolean;
  discount?: number;
}

interface BookRecommendationProps {
  title?: string;
  type?: 'personalized' | 'trending' | 'new' | 'similar';
  userId?: string;
  bookId?: string;
  limit?: number;
  className?: string;
}

const RecommendationContainer = styled.div`
  .recommendation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: ${theme.spacing[6]};
    
    .header-left {
      display: flex;
      align-items: center;
      gap: ${theme.spacing[3]};
      
      .header-icon {
        font-size: ${theme.typography.fontSize['2xl']};
        color: ${theme.colors.primary[500]};
      }
      
      .header-title {
        font-size: ${theme.typography.fontSize['2xl']};
        font-weight: ${theme.typography.fontWeight.bold};
        color: ${theme.colors.gray[800]};
        margin: 0;
      }
      
      .header-subtitle {
        color: ${theme.colors.gray[500]};
        font-size: ${theme.typography.fontSize.sm};
        margin-left: ${theme.spacing[2]};
      }
    }
    
    .header-right {
      .view-more-btn {
        color: ${theme.colors.primary[500]};
        font-weight: ${theme.typography.fontWeight.medium};
        
        &:hover {
          color: ${theme.colors.primary[600]};
        }
      }
    }
  }
  
  .books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: ${theme.spacing[6]};
    
    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: ${theme.spacing[4]};
    }
  }
`;

const BookCard = styled(Card)`
  height: 100%;
  border-radius: ${theme.borderRadius['2xl']};
  overflow: hidden;
  transition: all ${theme.animation.duration.base};
  border: 1px solid ${theme.colors.gray[200]};
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${theme.boxShadow['2xl']};
    border-color: ${theme.colors.primary[200]};
  }
  
  .ant-card-body {
    padding: 0;
  }
  
  .book-cover {
    position: relative;
    height: 200px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform ${theme.animation.duration.base};
    }
    
    &:hover img {
      transform: scale(1.05);
    }
    
    .book-badges {
      position: absolute;
      top: ${theme.spacing[3]};
      left: ${theme.spacing[3]};
      display: flex;
      flex-direction: column;
      gap: ${theme.spacing[2]};
      
      .badge {
        padding: ${theme.spacing[1]} ${theme.spacing[2]};
        border-radius: ${theme.borderRadius.full};
        font-size: ${theme.typography.fontSize.xs};
        font-weight: ${theme.typography.fontWeight.bold};
        color: white;
        
        &.hot {
          background: ${theme.colors.gradients.sunset};
        }
        
        &.new {
          background: ${theme.colors.gradients.ocean};
        }
        
        &.discount {
          background: ${theme.colors.gradients.forest};
        }
      }
    }
    
    .book-actions {
      position: absolute;
      top: ${theme.spacing[3]};
      right: ${theme.spacing[3]};
      display: flex;
      flex-direction: column;
      gap: ${theme.spacing[2]};
      opacity: 0;
      transition: opacity ${theme.animation.duration.base};
      
      .action-btn {
        width: 36px;
        height: 36px;
        border-radius: ${theme.borderRadius.full};
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all ${theme.animation.duration.fast};
        
        &:hover {
          background: white;
          transform: scale(1.1);
        }
        
        .anticon {
          color: ${theme.colors.gray[600]};
        }
      }
    }
    
    &:hover .book-actions {
      opacity: 1;
    }
  }
  
  .book-info {
    padding: ${theme.spacing[5]};
    
    .book-title {
      font-size: ${theme.typography.fontSize.lg};
      font-weight: ${theme.typography.fontWeight.bold};
      color: ${theme.colors.gray[800]};
      margin-bottom: ${theme.spacing[2]};
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .book-author {
      color: ${theme.colors.gray[500]};
      font-size: ${theme.typography.fontSize.sm};
      margin-bottom: ${theme.spacing[3]};
    }
    
    .book-rating {
      display: flex;
      align-items: center;
      gap: ${theme.spacing[2]};
      margin-bottom: ${theme.spacing[3]};
      
      .ant-rate {
        font-size: ${theme.typography.fontSize.sm};
      }
      
      .rating-text {
        color: ${theme.colors.gray[500]};
        font-size: ${theme.typography.fontSize.sm};
      }
    }
    
    .book-tags {
      display: flex;
      flex-wrap: wrap;
      gap: ${theme.spacing[2]};
      margin-bottom: ${theme.spacing[4]};
      
      .ant-tag {
        border-radius: ${theme.borderRadius.full};
        border: none;
        background: ${theme.colors.gray[100]};
        color: ${theme.colors.gray[600]};
        font-size: ${theme.typography.fontSize.xs};
      }
    }
    
    .book-price {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: ${theme.spacing[4]};
      
      .price-info {
        display: flex;
        align-items: center;
        gap: ${theme.spacing[2]};
        
        .current-price {
          font-size: ${theme.typography.fontSize.xl};
          font-weight: ${theme.typography.fontWeight.bold};
          color: ${theme.colors.error[500]};
        }
        
        .original-price {
          font-size: ${theme.typography.fontSize.sm};
          color: ${theme.colors.gray[400]};
          text-decoration: line-through;
        }
      }
      
      .discount-rate {
        background: ${theme.colors.error[500]};
        color: white;
        padding: ${theme.spacing[1]} ${theme.spacing[2]};
        border-radius: ${theme.borderRadius.md};
        font-size: ${theme.typography.fontSize.xs};
        font-weight: ${theme.typography.fontWeight.bold};
      }
    }
    
    .book-actions-bottom {
      display: flex;
      gap: ${theme.spacing[3]};
      
      .add-to-cart-btn {
        flex: 1;
      }
      
      .quick-view-btn {
        flex-shrink: 0;
      }
    }
  }
`;

const BookRecommendation: React.FC<BookRecommendationProps> = ({
  title,
  type = 'personalized',
  userId,
  bookId,
  limit = 8,
  className
}) => {
  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRecommendations();
  }, [type, userId, bookId, limit]);

  const loadRecommendations = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟推荐数据
      const mockBooks: Book[] = [
        {
          id: '1',
          title: '人工智能：一种现代的方法',
          author: 'Stuart Russell',
          cover: '/api/placeholder/280/200',
          price: 89.99,
          originalPrice: 129.99,
          rating: 4.8,
          reviewCount: 256,
          tags: ['人工智能', '计算机科学', '机器学习'],
          description: '人工智能领域的经典教材，全面介绍AI的理论与实践。',
          isHot: true,
          discount: 31
        },
        {
          id: '2',
          title: '深度学习',
          author: 'Ian Goodfellow',
          cover: '/api/placeholder/280/200',
          price: 79.99,
          originalPrice: 99.99,
          rating: 4.7,
          reviewCount: 189,
          tags: ['深度学习', '神经网络', '机器学习'],
          description: '深度学习领域的权威著作，由领域专家撰写。',
          isNew: true,
          discount: 20
        }
      ];
      
      setBooks(mockBooks);
    } catch (error) {
      console.error('Failed to load recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const getHeaderInfo = () => {
    switch (type) {
      case 'trending':
        return {
          icon: <FireOutlined />,
          title: '热门推荐',
          subtitle: '当前最受欢迎的图书'
        };
      case 'new':
        return {
          icon: <StarOutlined />,
          title: '新书上架',
          subtitle: '最新添加的优质图书'
        };
      case 'similar':
        return {
          icon: <BookOutlined />,
          title: '相似推荐',
          subtitle: '您可能感兴趣的图书'
        };
      default:
        return {
          icon: <HeartOutlined />,
          title: title || '为您推荐',
          subtitle: '基于您的喜好精选'
        };
    }
  };

  const headerInfo = getHeaderInfo();

  const handleAddToCart = (book: Book) => {
    console.log('Add to cart:', book);
    // 实现添加到购物车逻辑
  };

  const handleQuickView = (book: Book) => {
    console.log('Quick view:', book);
    // 实现快速查看逻辑
  };

  const handleToggleFavorite = (book: Book) => {
    console.log('Toggle favorite:', book);
    // 实现收藏切换逻辑
  };

  if (loading) {
    return (
      <RecommendationContainer className={className}>
        <div className="recommendation-header">
          <div className="header-left">
            <Skeleton.Avatar size="large" />
            <Skeleton.Input style={{ width: 200 }} active />
          </div>
        </div>
        <div className="books-grid">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index}>
              <Skeleton loading active>
                <div style={{ height: 300 }} />
              </Skeleton>
            </Card>
          ))}
        </div>
      </RecommendationContainer>
    );
  }

  return (
    <RecommendationContainer className={className}>
      <div className="recommendation-header">
        <div className="header-left">
          <span className="header-icon">{headerInfo.icon}</span>
          <div>
            <h2 className="header-title">{headerInfo.title}</h2>
            <span className="header-subtitle">{headerInfo.subtitle}</span>
          </div>
        </div>
        <div className="header-right">
          <Button variant="link" className="view-more-btn">
            查看更多 →
          </Button>
        </div>
      </div>

      <div className="books-grid">
        {books.map((book, index) => (
          <AnimatedWrapper
            key={book.id}
            animation="slideInUp"
            delay={`${index * 0.1}s`}
            trigger="visible"
          >
            <BookCard hoverable>
              <div className="book-cover">
                <img src={book.cover} alt={book.title} />
                
                <div className="book-badges">
                  {book.isHot && <span className="badge hot">🔥 热门</span>}
                  {book.isNew && <span className="badge new">✨ 新品</span>}
                  {book.discount && (
                    <span className="badge discount">-{book.discount}%</span>
                  )}
                </div>
                
                <div className="book-actions">
                  <button 
                    className="action-btn"
                    onClick={() => handleToggleFavorite(book)}
                  >
                    <HeartOutlined />
                  </button>
                  <button 
                    className="action-btn"
                    onClick={() => handleQuickView(book)}
                  >
                    <EyeOutlined />
                  </button>
                </div>
              </div>
              
              <div className="book-info">
                <h3 className="book-title">{book.title}</h3>
                <p className="book-author">作者：{book.author}</p>
                
                <div className="book-rating">
                  <Rate disabled defaultValue={book.rating} allowHalf />
                  <span className="rating-text">
                    {book.rating} ({book.reviewCount}条评价)
                  </span>
                </div>
                
                <div className="book-tags">
                  {book.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </div>
                
                <div className="book-price">
                  <div className="price-info">
                    <span className="current-price">¥{book.price}</span>
                    {book.originalPrice > book.price && (
                      <span className="original-price">¥{book.originalPrice}</span>
                    )}
                  </div>
                  {book.discount && (
                    <span className="discount-rate">省{book.discount}%</span>
                  )}
                </div>
                
                <div className="book-actions-bottom">
                  <Button
                    variant="gradient"
                    gradient="primary"
                    size="small"
                    className="add-to-cart-btn"
                    onClick={() => handleAddToCart(book)}
                    rounded
                  >
                    <ShoppingCartOutlined /> 加入购物车
                  </Button>
                  <Button
                    variant="ghost"
                    size="small"
                    className="quick-view-btn"
                    onClick={() => handleQuickView(book)}
                    rounded
                  >
                    <EyeOutlined />
                  </Button>
                </div>
              </div>
            </BookCard>
          </AnimatedWrapper>
        ))}
      </div>
    </RecommendationContainer>
  );
};

export default BookRecommendation;
