import React from 'react';
import { Statistic, Progress } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  TrophyOutlined,
  FireOutlined,
  ThunderboltOutlined,
  StarOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import Card from './Card';
import { theme } from '../../styles/theme';

export type StatCardVariant = 'default' | 'trend' | 'progress' | 'achievement' | 'gradient';
export type TrendType = 'up' | 'down' | 'stable';

interface StatCardProps {
  title: string;
  value: string | number;
  suffix?: string;
  prefix?: string;
  icon?: React.ReactNode;
  variant?: StatCardVariant;
  trend?: TrendType;
  trendValue?: string | number;
  progress?: number;
  progressColor?: string;
  description?: string;
  color?: string;
  gradient?: keyof typeof theme.colors.gradients;
  size?: 'small' | 'medium' | 'large';
  hoverable?: boolean;
  onClick?: () => void;
}

const StatCardContainer = styled.div<{ 
  variant: StatCardVariant; 
  size: 'small' | 'medium' | 'large';
  gradient?: keyof typeof theme.colors.gradients;
  color?: string;
  hoverable?: boolean;
}>`
  position: relative;
  height: 100%;
  cursor: ${({ onClick, hoverable }) => (onClick || hoverable) ? 'pointer' : 'default'};
  
  .stat-card-content {
    padding: ${({ size }) => {
      switch (size) {
        case 'small':
          return theme.spacing[4];
        case 'large':
          return theme.spacing[8];
        default:
          return theme.spacing[6];
      }
    }};
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    
    ${({ variant, gradient, color }) => {
      if (variant === 'gradient' && gradient) {
        return `
          background: ${theme.colors.gradients[gradient]};
          color: white;
          border-radius: ${theme.borderRadius['2xl']};
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
            border-radius: ${theme.borderRadius['2xl']};
          }
        `;
      }
      
      if (color) {
        return `
          border-left: 4px solid ${color};
        `;
      }
      
      return '';
    }}
  }
  
  .stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: ${theme.spacing[4]};
    
    .stat-icon {
      width: ${({ size }) => {
        switch (size) {
          case 'small':
            return '32px';
          case 'large':
            return '48px';
          default:
            return '40px';
        }
      }};
      height: ${({ size }) => {
        switch (size) {
          case 'small':
            return '32px';
          case 'large':
            return '48px';
          default:
            return '40px';
        }
      }};
      border-radius: ${theme.borderRadius.lg};
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: ${({ size }) => {
        switch (size) {
          case 'small':
            return theme.typography.fontSize.lg;
          case 'large':
            return theme.typography.fontSize['2xl'];
          default:
            return theme.typography.fontSize.xl;
        }
      }};
      background: ${({ variant }) => 
        variant === 'gradient' ? 'rgba(255, 255, 255, 0.2)' : theme.colors.gray[100]
      };
      color: ${({ variant, color }) => {
        if (variant === 'gradient') return 'white';
        if (color) return color;
        return theme.colors.primary[500];
      }};
    }
    
    .stat-title {
      font-size: ${({ size }) => {
        switch (size) {
          case 'small':
            return theme.typography.fontSize.sm;
          case 'large':
            return theme.typography.fontSize.lg;
          default:
            return theme.typography.fontSize.base;
        }
      }};
      color: ${({ variant }) => 
        variant === 'gradient' ? 'rgba(255, 255, 255, 0.9)' : theme.colors.gray[600]
      };
      font-weight: ${theme.typography.fontWeight.medium};
      margin: 0;
    }
  }
  
  .stat-value {
    .ant-statistic-content {
      font-size: ${({ size }) => {
        switch (size) {
          case 'small':
            return theme.typography.fontSize['2xl'];
          case 'large':
            return theme.typography.fontSize['4xl'];
          default:
            return theme.typography.fontSize['3xl'];
        }
      }};
      font-weight: ${theme.typography.fontWeight.bold};
      color: ${({ variant }) => 
        variant === 'gradient' ? 'white' : theme.colors.gray[800]
      };
    }
  }
  
  .stat-trend {
    display: flex;
    align-items: center;
    gap: ${theme.spacing[1]};
    margin-top: ${theme.spacing[2]};
    font-size: ${theme.typography.fontSize.sm};
    
    &.trend-up {
      color: ${({ variant }) => 
        variant === 'gradient' ? 'rgba(255, 255, 255, 0.9)' : theme.colors.success[500]
      };
    }
    
    &.trend-down {
      color: ${({ variant }) => 
        variant === 'gradient' ? 'rgba(255, 255, 255, 0.9)' : theme.colors.error[500]
      };
    }
    
    &.trend-stable {
      color: ${({ variant }) => 
        variant === 'gradient' ? 'rgba(255, 255, 255, 0.9)' : theme.colors.gray[500]
      };
    }
  }
  
  .stat-progress {
    margin-top: ${theme.spacing[4]};
    
    .ant-progress-text {
      color: ${({ variant }) => 
        variant === 'gradient' ? 'white' : 'inherit'
      } !important;
    }
  }
  
  .stat-description {
    margin-top: ${theme.spacing[3]};
    font-size: ${theme.typography.fontSize.sm};
    color: ${({ variant }) => 
      variant === 'gradient' ? 'rgba(255, 255, 255, 0.8)' : theme.colors.gray[500]
    };
    line-height: ${theme.typography.lineHeight.relaxed};
  }
  
  /* 悬停效果 */
  ${({ hoverable, onClick }) => (hoverable || onClick) && `
    transition: transform ${theme.animation.duration.base};
    
    &:hover {
      transform: translateY(-4px);
      
      .stat-icon {
        transform: scale(1.1);
      }
    }
  `}
`;

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  suffix,
  prefix,
  icon,
  variant = 'default',
  trend,
  trendValue,
  progress,
  progressColor,
  description,
  color,
  gradient,
  size = 'medium',
  hoverable = false,
  onClick
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <ArrowUpOutlined />;
      case 'down':
        return <ArrowDownOutlined />;
      default:
        return null;
    }
  };

  const getDefaultIcon = () => {
    if (icon) return icon;
    
    switch (variant) {
      case 'achievement':
        return <TrophyOutlined />;
      case 'trend':
        return <ThunderboltOutlined />;
      case 'progress':
        return <StarOutlined />;
      default:
        return <FireOutlined />;
    }
  };

  return (
    <Card
      variant={variant === 'gradient' ? 'default' : 'elevated'}
      hoverable={hoverable || !!onClick}
      onClick={onClick}
    >
      <StatCardContainer
        variant={variant}
        size={size}
        gradient={gradient}
        color={color}
        hoverable={hoverable}
        onClick={onClick}
      >
        <div className="stat-card-content">
          <div className="stat-header">
            <div className="stat-icon">
              {getDefaultIcon()}
            </div>
            <div className="stat-title">{title}</div>
          </div>
          
          <div className="stat-value">
            <Statistic
              value={value}
              suffix={suffix}
              prefix={prefix}
              valueStyle={{
                color: variant === 'gradient' ? 'white' : theme.colors.gray[800]
              }}
            />
          </div>
          
          {trend && trendValue && (
            <div className={`stat-trend trend-${trend}`}>
              {getTrendIcon()}
              <span>{trendValue}</span>
            </div>
          )}
          
          {progress !== undefined && (
            <div className="stat-progress">
              <Progress
                percent={progress}
                strokeColor={progressColor || (variant === 'gradient' ? 'white' : undefined)}
                trailColor={variant === 'gradient' ? 'rgba(255, 255, 255, 0.3)' : undefined}
                size="small"
                showInfo={true}
              />
            </div>
          )}
          
          {description && (
            <div className="stat-description">
              {description}
            </div>
          )}
        </div>
      </StatCardContainer>
    </Card>
  );
};

export default StatCard;
