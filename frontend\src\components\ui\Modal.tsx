import React from 'react';
import { Modal as AntModal, ModalProps as AntModalProps } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined, InfoCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import Button from './Button';
import { theme } from '../../styles/theme';

export type ModalVariant = 'default' | 'confirm' | 'info' | 'success' | 'warning' | 'error';
export type ModalSize = 'small' | 'medium' | 'large' | 'fullscreen';

interface CustomModalProps extends Omit<AntModalProps, 'size'> {
  variant?: ModalVariant;
  size?: ModalSize;
  showIcon?: boolean;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  gradient?: boolean;
}

const StyledModal = styled(AntModal)<{ 
  variant: ModalVariant; 
  size: ModalSize; 
  gradient?: boolean;
}>`
  .ant-modal-content {
    border-radius: ${theme.borderRadius['2xl']};
    overflow: hidden;
    box-shadow: ${theme.boxShadow['2xl']};
    border: 1px solid ${theme.colors.gray[200]};
    
    ${({ gradient, variant }) => gradient && `
      background: ${theme.colors.gradients.primary};
      color: white;
      
      .ant-modal-header {
        background: transparent;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        
        .ant-modal-title {
          color: white;
        }
      }
      
      .ant-modal-body {
        color: rgba(255, 255, 255, 0.9);
      }
      
      .ant-modal-footer {
        background: transparent;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
      }
    `}
  }
  
  .ant-modal-header {
    background: ${theme.colors.gray[50]};
    border-bottom: 1px solid ${theme.colors.gray[200]};
    padding: ${theme.spacing[6]} ${theme.spacing[6]} ${theme.spacing[4]} ${theme.spacing[6]};
    
    .ant-modal-title {
      font-size: ${theme.typography.fontSize.xl};
      font-weight: ${theme.typography.fontWeight.bold};
      color: ${theme.colors.gray[800]};
      display: flex;
      align-items: center;
      gap: ${theme.spacing[3]};
    }
  }
  
  .ant-modal-body {
    padding: ${theme.spacing[6]};
    font-size: ${theme.typography.fontSize.base};
    line-height: ${theme.typography.lineHeight.relaxed};
    color: ${theme.colors.gray[700]};
  }
  
  .ant-modal-footer {
    background: ${theme.colors.gray[50]};
    border-top: 1px solid ${theme.colors.gray[200]};
    padding: ${theme.spacing[4]} ${theme.spacing[6]};
    
    .ant-btn + .ant-btn {
      margin-left: ${theme.spacing[3]};
    }
  }
  
  .ant-modal-close {
    top: ${theme.spacing[4]};
    right: ${theme.spacing[4]};
    
    .ant-modal-close-x {
      width: 40px;
      height: 40px;
      line-height: 40px;
      font-size: ${theme.typography.fontSize.lg};
      color: ${theme.colors.gray[500]};
      border-radius: ${theme.borderRadius.full};
      transition: all ${theme.animation.duration.fast};
      
      &:hover {
        background: ${theme.colors.gray[100]};
        color: ${theme.colors.gray[700]};
      }
    }
  }
  
  /* 尺寸样式 */
  ${({ size }) => {
    switch (size) {
      case 'small':
        return `
          .ant-modal {
            max-width: 400px;
          }
        `;
      case 'large':
        return `
          .ant-modal {
            max-width: 800px;
          }
        `;
      case 'fullscreen':
        return `
          .ant-modal {
            max-width: 100vw;
            margin: 0;
            padding: 0;
            height: 100vh;
          }
          
          .ant-modal-content {
            height: 100vh;
            border-radius: 0;
          }
        `;
      default:
        return `
          .ant-modal {
            max-width: 600px;
          }
        `;
    }
  }}
  
  /* 变体样式 */
  ${({ variant }) => {
    const getVariantColor = () => {
      switch (variant) {
        case 'success':
          return theme.colors.success[500];
        case 'warning':
          return theme.colors.warning[500];
        case 'error':
          return theme.colors.error[500];
        case 'info':
          return theme.colors.primary[500];
        default:
          return theme.colors.primary[500];
      }
    };
    
    const color = getVariantColor();
    
    return `
      .ant-modal-header {
        border-left: 4px solid ${color};
      }
      
      .modal-icon {
        color: ${color};
        font-size: ${theme.typography.fontSize['2xl']};
      }
    `;
  }}
`;

const ModalContent = styled.div`
  .modal-icon-wrapper {
    display: flex;
    align-items: flex-start;
    gap: ${theme.spacing[4]};
    
    .modal-icon {
      flex-shrink: 0;
      margin-top: ${theme.spacing[1]};
    }
    
    .modal-text {
      flex: 1;
    }
  }
`;

const Modal: React.FC<CustomModalProps> = ({
  variant = 'default',
  size = 'medium',
  showIcon = true,
  confirmText = '确定',
  cancelText = '取消',
  onConfirm,
  onCancel,
  loading = false,
  gradient = false,
  title,
  children,
  footer,
  ...props
}) => {
  const getIcon = () => {
    if (!showIcon) return null;
    
    switch (variant) {
      case 'confirm':
        return <ExclamationCircleOutlined className="modal-icon" />;
      case 'success':
        return <CheckCircleOutlined className="modal-icon" />;
      case 'warning':
        return <ExclamationCircleOutlined className="modal-icon" />;
      case 'error':
        return <CloseCircleOutlined className="modal-icon" />;
      case 'info':
        return <InfoCircleOutlined className="modal-icon" />;
      default:
        return null;
    }
  };

  const handleConfirm = async () => {
    if (onConfirm) {
      try {
        await onConfirm();
      } catch (error) {
        console.error('Modal confirm error:', error);
      }
    }
  };

  const renderTitle = () => {
    const icon = getIcon();
    if (icon && title) {
      return (
        <>
          {icon}
          {title}
        </>
      );
    }
    return title;
  };

  const renderContent = () => {
    const icon = getIcon();
    
    if (icon && children) {
      return (
        <ModalContent>
          <div className="modal-icon-wrapper">
            {icon}
            <div className="modal-text">{children}</div>
          </div>
        </ModalContent>
      );
    }
    
    return children;
  };

  const renderFooter = () => {
    if (footer === null) return null;
    
    if (footer) return footer;
    
    return [
      <Button
        key="cancel"
        variant="ghost"
        onClick={onCancel}
      >
        {cancelText}
      </Button>,
      <Button
        key="confirm"
        variant={gradient ? 'gradient' : 'primary'}
        gradient={gradient ? 'primary' : undefined}
        loading={loading}
        onClick={handleConfirm}
        rounded
        elevated
      >
        {confirmText}
      </Button>
    ];
  };

  return (
    <StyledModal
      variant={variant}
      size={size}
      gradient={gradient}
      title={renderTitle()}
      footer={renderFooter()}
      onCancel={onCancel}
      {...props}
    >
      {renderContent()}
    </StyledModal>
  );
};

// 静态方法
Modal.confirm = (props: CustomModalProps) => {
  return AntModal.confirm({
    ...props,
    icon: props.showIcon !== false ? <ExclamationCircleOutlined /> : null,
    okText: props.confirmText || '确定',
    cancelText: props.cancelText || '取消',
    onOk: props.onConfirm,
    onCancel: props.onCancel
  });
};

Modal.info = (props: CustomModalProps) => {
  return AntModal.info({
    ...props,
    icon: props.showIcon !== false ? <InfoCircleOutlined /> : null,
    okText: props.confirmText || '确定',
    onOk: props.onConfirm
  });
};

Modal.success = (props: CustomModalProps) => {
  return AntModal.success({
    ...props,
    icon: props.showIcon !== false ? <CheckCircleOutlined /> : null,
    okText: props.confirmText || '确定',
    onOk: props.onConfirm
  });
};

Modal.warning = (props: CustomModalProps) => {
  return AntModal.warning({
    ...props,
    icon: props.showIcon !== false ? <ExclamationCircleOutlined /> : null,
    okText: props.confirmText || '确定',
    onOk: props.onConfirm
  });
};

Modal.error = (props: CustomModalProps) => {
  return AntModal.error({
    ...props,
    icon: props.showIcon !== false ? <CloseCircleOutlined /> : null,
    okText: props.confirmText || '确定',
    onOk: props.onConfirm
  });
};

export default Modal;
