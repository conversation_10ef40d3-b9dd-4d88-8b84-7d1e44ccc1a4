import React, { useEffect, useRef, useState } from 'react';
import styled, { css } from 'styled-components';
import { animations, hoverEffects, clickEffects } from '../../utils/animations';

export type AnimationType = 
  | 'fadeIn' 
  | 'slideInUp' 
  | 'slideInDown' 
  | 'slideInLeft' 
  | 'slideInRight' 
  | 'scaleIn' 
  | 'bounceIn';

export type HoverEffect = 'lift' | 'scale' | 'shadow' | 'glow' | 'rotate' | 'skew';
export type ClickEffect = 'press' | 'ripple';

interface AnimatedWrapperProps {
  children: React.ReactNode;
  animation?: AnimationType;
  duration?: string;
  delay?: string;
  hover?: HoverEffect;
  click?: ClickEffect;
  trigger?: 'mount' | 'visible' | 'manual';
  threshold?: number;
  once?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onAnimationStart?: () => void;
  onAnimationEnd?: () => void;
}

const AnimatedContainer = styled.div<{
  animation?: AnimationType;
  duration?: string;
  delay?: string;
  hover?: HoverEffect;
  click?: ClickEffect;
  isVisible?: boolean;
  trigger?: 'mount' | 'visible' | 'manual';
}>`
  ${({ animation, duration, delay, isVisible, trigger }) => {
    if (!animation || (trigger === 'visible' && !isVisible)) return '';
    
    switch (animation) {
      case 'fadeIn':
        return animations.fadeIn(duration, delay);
      case 'slideInUp':
        return animations.slideInUp(duration, delay);
      case 'slideInDown':
        return animations.slideInDown(duration, delay);
      case 'slideInLeft':
        return animations.slideInLeft(duration, delay);
      case 'slideInRight':
        return animations.slideInRight(duration, delay);
      case 'scaleIn':
        return animations.scaleIn(duration, delay);
      case 'bounceIn':
        return animations.bounceIn(duration, delay);
      default:
        return '';
    }
  }}
  
  ${({ hover }) => {
    switch (hover) {
      case 'lift':
        return hoverEffects.lift;
      case 'scale':
        return hoverEffects.scale;
      case 'shadow':
        return hoverEffects.shadow;
      case 'glow':
        return hoverEffects.glow;
      case 'rotate':
        return hoverEffects.rotate;
      case 'skew':
        return hoverEffects.skew;
      default:
        return '';
    }
  }}
  
  ${({ click }) => {
    switch (click) {
      case 'press':
        return clickEffects.press;
      case 'ripple':
        return clickEffects.ripple;
      default:
        return '';
    }
  }}
  
  ${({ trigger, isVisible }) => {
    if (trigger === 'visible' && !isVisible) {
      return css`
        opacity: 0;
        transform: translateY(20px);
      `;
    }
    return '';
  }}
`;

const AnimatedWrapper: React.FC<AnimatedWrapperProps> = ({
  children,
  animation,
  duration,
  delay,
  hover,
  click,
  trigger = 'mount',
  threshold = 0.1,
  once = true,
  className,
  style,
  onAnimationStart,
  onAnimationEnd
}) => {
  const [isVisible, setIsVisible] = useState(trigger === 'mount');
  const [hasAnimated, setHasAnimated] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (trigger !== 'visible') return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && (!once || !hasAnimated)) {
          setIsVisible(true);
          setHasAnimated(true);
          onAnimationStart?.();
        } else if (!once && !entry.isIntersecting) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin: '50px'
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => {
      if (elementRef.current) {
        observer.unobserve(elementRef.current);
      }
    };
  }, [trigger, threshold, once, hasAnimated, onAnimationStart]);

  useEffect(() => {
    if (isVisible && animation) {
      const timer = setTimeout(() => {
        onAnimationEnd?.();
      }, parseFloat(duration || '0.3') * 1000 + parseFloat(delay || '0') * 1000);

      return () => clearTimeout(timer);
    }
  }, [isVisible, animation, duration, delay, onAnimationEnd]);

  return (
    <AnimatedContainer
      ref={elementRef}
      animation={animation}
      duration={duration}
      delay={delay}
      hover={hover}
      click={click}
      isVisible={isVisible}
      trigger={trigger}
      className={className}
      style={style}
    >
      {children}
    </AnimatedContainer>
  );
};

export default AnimatedWrapper;
