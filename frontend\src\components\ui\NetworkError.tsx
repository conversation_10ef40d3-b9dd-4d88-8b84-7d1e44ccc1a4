import React from 'react';
import { Result } from 'antd';
import {
  WifiOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  LockOutlined,
  StopOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import Button from './Button';
import { theme } from '../../styles/theme';

export type NetworkErrorType = 
  | 'offline' 
  | 'timeout' 
  | 'server-error' 
  | 'forbidden' 
  | 'not-found' 
  | 'rate-limit'
  | 'unknown';

interface NetworkErrorProps {
  type?: NetworkErrorType;
  title?: string;
  description?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
  showRetry?: boolean;
  showGoBack?: boolean;
  retryText?: string;
  goBackText?: string;
  className?: string;
}

const ErrorContainer = styled.div`
  min-height: 40vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing[8]};
  
  .ant-result {
    .ant-result-icon {
      margin-bottom: ${theme.spacing[6]};
      
      .anticon {
        font-size: ${theme.typography.fontSize['5xl']};
        animation: pulse 2s infinite;
      }
    }
    
    .ant-result-title {
      font-size: ${theme.typography.fontSize['2xl']};
      font-weight: ${theme.typography.fontWeight.bold};
      color: ${theme.colors.gray[800]};
      margin-bottom: ${theme.spacing[4]};
    }
    
    .ant-result-subtitle {
      font-size: ${theme.typography.fontSize.base};
      color: ${theme.colors.gray[600]};
      line-height: ${theme.typography.lineHeight.relaxed};
      margin-bottom: ${theme.spacing[8]};
    }
    
    .ant-result-extra {
      .error-actions {
        display: flex;
        gap: ${theme.spacing[4]};
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
  }
`;

const NetworkError: React.FC<NetworkErrorProps> = ({
  type = 'unknown',
  title,
  description,
  onRetry,
  onGoBack,
  showRetry = true,
  showGoBack = false,
  retryText = '重试',
  goBackText = '返回',
  className
}) => {
  const getErrorConfig = () => {
    switch (type) {
      case 'offline':
        return {
          icon: <WifiOutlined style={{ color: theme.colors.error[500] }} />,
          status: 'error' as const,
          title: title || '网络连接失败',
          description: description || '请检查您的网络连接，然后重试。'
        };
        
      case 'timeout':
        return {
          icon: <ClockCircleOutlined style={{ color: theme.colors.warning[500] }} />,
          status: 'warning' as const,
          title: title || '请求超时',
          description: description || '服务器响应时间过长，请稍后重试。'
        };
        
      case 'server-error':
        return {
          icon: <ExclamationCircleOutlined style={{ color: theme.colors.error[500] }} />,
          status: 'error' as const,
          title: title || '服务器错误',
          description: description || '服务器遇到了问题，请稍后重试或联系技术支持。'
        };
        
      case 'forbidden':
        return {
          icon: <LockOutlined style={{ color: theme.colors.error[500] }} />,
          status: 'error' as const,
          title: title || '访问被拒绝',
          description: description || '您没有权限访问此资源，请联系管理员。'
        };
        
      case 'not-found':
        return {
          icon: <ExclamationCircleOutlined style={{ color: theme.colors.warning[500] }} />,
          status: 'warning' as const,
          title: title || '资源未找到',
          description: description || '请求的资源不存在或已被移除。'
        };
        
      case 'rate-limit':
        return {
          icon: <StopOutlined style={{ color: theme.colors.warning[500] }} />,
          status: 'warning' as const,
          title: title || '请求过于频繁',
          description: description || '您的请求过于频繁，请稍后再试。'
        };
        
      default:
        return {
          icon: <ExclamationCircleOutlined style={{ color: theme.colors.error[500] }} />,
          status: 'error' as const,
          title: title || '发生了错误',
          description: description || '出现了意外错误，请重试或联系技术支持。'
        };
    }
  };

  const config = getErrorConfig();

  const renderActions = () => {
    const actions = [];
    
    if (showRetry && onRetry) {
      actions.push(
        <Button
          key="retry"
          variant="gradient"
          gradient="primary"
          size="large"
          onClick={onRetry}
          rounded
          elevated
        >
          <ReloadOutlined /> {retryText}
        </Button>
      );
    }
    
    if (showGoBack && onGoBack) {
      actions.push(
        <Button
          key="goback"
          variant="ghost"
          size="large"
          onClick={onGoBack}
          rounded
        >
          {goBackText}
        </Button>
      );
    }
    
    return actions.length > 0 ? (
      <div className="error-actions">
        {actions}
      </div>
    ) : null;
  };

  return (
    <ErrorContainer className={className}>
      <Result
        icon={config.icon}
        title={config.title}
        subTitle={config.description}
        extra={renderActions()}
      />
    </ErrorContainer>
  );
};

export default NetworkError;
