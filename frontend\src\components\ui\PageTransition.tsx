import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import styled, { css } from 'styled-components';
import { theme } from '../../styles/theme';
import { pageTransitions } from '../../utils/animations';

export type TransitionType = 'fade' | 'slide' | 'scale' | 'none';

interface PageTransitionProps {
  children: React.ReactNode;
  type?: TransitionType;
  duration?: number;
  className?: string;
}

const TransitionContainer = styled.div<{
  type: TransitionType;
  duration: number;
  isEntering: boolean;
  isExiting: boolean;
}>`
  width: 100%;
  min-height: 100vh;
  
  ${({ type, duration, isEntering, isExiting }) => {
    if (type === 'none') return '';
    
    const durationMs = `${duration}ms`;
    
    switch (type) {
      case 'fade':
        return css`
          opacity: ${isEntering ? 1 : isExiting ? 0 : 1};
          transition: opacity ${durationMs} ${theme.animation.easing.easeOut};
        `;
        
      case 'slide':
        return css`
          transform: translateX(${isEntering ? '0%' : isExiting ? '-100%' : '0%'});
          transition: transform ${durationMs} ${theme.animation.easing.easeOut};
        `;
        
      case 'scale':
        return css`
          transform: scale(${isEntering ? 1 : isExiting ? 0.95 : 1});
          opacity: ${isEntering ? 1 : isExiting ? 0 : 1};
          transition: transform ${durationMs} ${theme.animation.easing.easeOut},
                      opacity ${durationMs} ${theme.animation.easing.easeOut};
        `;
        
      default:
        return '';
    }
  }}
`;

const LoadingOverlay = styled.div<{ show: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${theme.zIndex.overlay};
  opacity: ${({ show }) => (show ? 1 : 0)};
  visibility: ${({ show }) => (show ? 'visible' : 'hidden')};
  transition: opacity 200ms ease, visibility 200ms ease;
  
  .loading-content {
    text-align: center;
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid ${theme.colors.gray[200]};
      border-top: 3px solid ${theme.colors.primary[500]};
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }
    
    .loading-text {
      color: ${theme.colors.gray[600]};
      font-size: ${theme.typography.fontSize.base};
      font-weight: ${theme.typography.fontWeight.medium};
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  type = 'fade',
  duration = 300,
  className
}) => {
  const location = useLocation();
  const [isEntering, setIsEntering] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [displayChildren, setDisplayChildren] = useState(children);

  useEffect(() => {
    if (type === 'none') {
      setDisplayChildren(children);
      return;
    }

    // 开始退出动画
    setIsExiting(true);
    setIsLoading(true);
    
    const exitTimer = setTimeout(() => {
      // 更新内容
      setDisplayChildren(children);
      setIsExiting(false);
      setIsEntering(true);
      
      // 开始进入动画
      const enterTimer = setTimeout(() => {
        setIsEntering(false);
        setIsLoading(false);
      }, duration);
      
      return () => clearTimeout(enterTimer);
    }, duration);

    return () => clearTimeout(exitTimer);
  }, [location.pathname, children, type, duration]);

  // 页面首次加载时的进入动画
  useEffect(() => {
    if (type !== 'none') {
      setIsEntering(true);
      const timer = setTimeout(() => {
        setIsEntering(false);
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, []);

  return (
    <>
      <TransitionContainer
        type={type}
        duration={duration}
        isEntering={isEntering}
        isExiting={isExiting}
        className={className}
      >
        {displayChildren}
      </TransitionContainer>
      
      <LoadingOverlay show={isLoading && type !== 'none'}>
        <div className="loading-content">
          <div className="loading-spinner" />
          <div className="loading-text">页面切换中...</div>
        </div>
      </LoadingOverlay>
    </>
  );
};

export default PageTransition;
