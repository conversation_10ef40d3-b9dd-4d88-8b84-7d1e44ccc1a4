import styled, { createGlobalStyle } from 'styled-components';
import { theme } from './theme';

// 全局样式
export const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    font-family: ${theme.typography.fontFamily.base};
    font-size: ${theme.typography.fontSize.base};
    line-height: ${theme.typography.lineHeight.base};
    color: ${theme.colors.textPrimary};
    background-color: ${theme.colors.background};
    min-height: 100vh;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  // 链接样式
  a {
    color: ${theme.colors.primary[500]};
    text-decoration: none;
    transition: color ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};

    &:hover {
      color: ${theme.colors.primary[400]};
    }

    &:active {
      color: ${theme.colors.primary[600]};
    }
  }

  // 按钮重置
  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
  }

  // 输入框重置
  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    border: none;
    outline: none;
  }

  // 图片响应式
  img {
    max-width: 100%;
    height: auto;
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${theme.colors.gray[100]};
    border-radius: ${theme.borderRadius.base};
  }

  ::-webkit-scrollbar-thumb {
    background: ${theme.colors.gray[400]};
    border-radius: ${theme.borderRadius.base};

    &:hover {
      background: ${theme.colors.gray[500]};
    }
  }

  // 选择文本样式
  ::selection {
    background-color: ${theme.colors.primary[200]};
    color: ${theme.colors.white};
  }

  // 焦点样式
  :focus-visible {
    outline: 2px solid ${theme.colors.primary};
    outline-offset: 2px;
  }

  // 禁用状态
  :disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  // 隐藏元素
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  // 文本截断
  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

// 容器组件
export const Container = styled.div<{ maxWidth?: string; padding?: string }>`
  width: 100%;
  margin: 0 auto;
  padding: ${props => props.padding || `0 ${theme.spacing.md}`};
  max-width: ${props => props.maxWidth || '1200px'};

  ${theme.mediaQueries.sm} {
    max-width: 540px;
  }

  ${theme.mediaQueries.md} {
    max-width: 720px;
  }

  ${theme.mediaQueries.lg} {
    max-width: 960px;
  }

  ${theme.mediaQueries.xl} {
    max-width: 1140px;
  }

  ${theme.mediaQueries.xxl} {
    max-width: 1320px;
  }
`;

// 网格布局
export const Grid = styled.div<{ 
  cols?: number; 
  gap?: string; 
  responsive?: boolean 
}>`
  display: grid;
  gap: ${props => props.gap || theme.spacing.md};
  grid-template-columns: 1fr;

  ${props => props.responsive && `
    ${theme.mediaQueries.sm} {
      grid-template-columns: repeat(2, 1fr);
    }

    ${theme.mediaQueries.md} {
      grid-template-columns: repeat(3, 1fr);
    }

    ${theme.mediaQueries.lg} {
      grid-template-columns: repeat(${props.cols || 4}, 1fr);
    }
  `}

  ${props => !props.responsive && props.cols && `
    grid-template-columns: repeat(${props.cols}, 1fr);
  `}
`;

// Flex布局
export const Flex = styled.div<{
  direction?: 'row' | 'column';
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  wrap?: boolean;
  gap?: string;
}>`
  display: flex;
  flex-direction: ${props => props.direction || 'row'};
  justify-content: ${props => props.justify || 'flex-start'};
  align-items: ${props => props.align || 'stretch'};
  flex-wrap: ${props => props.wrap ? 'wrap' : 'nowrap'};
  gap: ${props => props.gap || '0'};
`;

// 卡片组件
export const Card = styled.div<{ padding?: string; shadow?: boolean }>`
  background: ${theme.colors.white};
  border-radius: ${theme.borderRadius.md};
  padding: ${props => props.padding || theme.spacing.md};
  ${props => props.shadow && `box-shadow: ${theme.boxShadow.base};`}
  border: 1px solid ${theme.colors.border};
  transition: box-shadow ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};

  &:hover {
    ${props => props.shadow && `box-shadow: ${theme.boxShadow.md};`}
  }
`;

// 响应式容器
export const ResponsiveContainer = styled.div`
  width: 100%;
  margin: 0 auto;
  padding: 0 ${theme.spacing[4]};

  @media (min-width: 576px) {
    padding: 0 ${theme.spacing[5]};
  }

  @media (min-width: 768px) {
    padding: 0 ${theme.spacing[6]};
  }

  @media (min-width: 992px) {
    max-width: 1200px;
    padding: 0 ${theme.spacing[8]};
  }

  @media (min-width: 1200px) {
    padding: 0 ${theme.spacing[10]};
  }
`;

// 响应式网格
export const ResponsiveGrid = styled.div<{
  gap?: string;
  minWidth?: string;
  columns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
}>`
  display: grid;
  gap: ${props => props.gap || theme.spacing[4]};
  grid-template-columns: repeat(${props => props.columns?.xs || 1}, 1fr);

  @media (min-width: 576px) {
    grid-template-columns: repeat(${props => props.columns?.sm || 2}, 1fr);
    gap: ${props => props.gap || theme.spacing[5]};
  }

  @media (min-width: 768px) {
    grid-template-columns: repeat(${props => props.columns?.md || 3}, 1fr);
    gap: ${props => props.gap || theme.spacing[6]};
  }

  @media (min-width: 992px) {
    grid-template-columns: repeat(${props => props.columns?.lg || 4}, 1fr);
  }

  @media (min-width: 1200px) {
    grid-template-columns: repeat(${props => props.columns?.xl || 5}, 1fr);
  }
`;
