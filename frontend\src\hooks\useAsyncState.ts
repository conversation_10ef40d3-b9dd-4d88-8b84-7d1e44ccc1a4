import { useState, useCallback, useRef, useEffect } from 'react';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  success: boolean;
}

export interface AsyncActions<T> {
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
  setData: (data: T | null) => void;
  setError: (error: Error | null) => void;
  setLoading: (loading: boolean) => void;
}

export interface UseAsyncStateOptions {
  initialData?: any;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  resetOnExecute?: boolean;
}

/**
 * 通用的异步状态管理Hook
 * 用于处理API请求、数据加载等异步操作的状态管理
 */
export function useAsyncState<T = any>(
  asyncFunction?: (...args: any[]) => Promise<T>,
  options: UseAsyncStateOptions = {}
): [AsyncState<T>, AsyncActions<T>] {
  const {
    initialData = null,
    onSuccess,
    onError,
    resetOnExecute = true
  } = options;

  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null,
    success: false
  });

  const cancelRef = useRef<boolean>(false);

  // 清理函数
  useEffect(() => {
    return () => {
      cancelRef.current = true;
    };
  }, []);

  const execute = useCallback(
    async (...args: any[]): Promise<T> => {
      if (!asyncFunction) {
        throw new Error('No async function provided');
      }

      // 重置取消标志
      cancelRef.current = false;

      // 重置状态（如果需要）
      if (resetOnExecute) {
        setState(prev => ({
          ...prev,
          loading: true,
          error: null,
          success: false
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: true,
          error: null
        }));
      }

      try {
        const result = await asyncFunction(...args);

        // 检查是否已取消
        if (cancelRef.current) {
          return result;
        }

        setState(prev => ({
          ...prev,
          data: result,
          loading: false,
          error: null,
          success: true
        }));

        // 调用成功回调
        if (onSuccess) {
          onSuccess(result);
        }

        return result;
      } catch (error) {
        // 检查是否已取消
        if (cancelRef.current) {
          throw error;
        }

        const errorObj = error instanceof Error ? error : new Error(String(error));

        setState(prev => ({
          ...prev,
          loading: false,
          error: errorObj,
          success: false
        }));

        // 调用错误回调
        if (onError) {
          onError(errorObj);
        }

        throw errorObj;
      }
    },
    [asyncFunction, resetOnExecute, onSuccess, onError]
  );

  const reset = useCallback(() => {
    cancelRef.current = true;
    setState({
      data: initialData,
      loading: false,
      error: null,
      success: false
    });
  }, [initialData]);

  const setData = useCallback((data: T | null) => {
    setState(prev => ({
      ...prev,
      data,
      success: data !== null
    }));
  }, []);

  const setError = useCallback((error: Error | null) => {
    setState(prev => ({
      ...prev,
      error,
      success: false
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      loading
    }));
  }, []);

  const actions: AsyncActions<T> = {
    execute,
    reset,
    setData,
    setError,
    setLoading
  };

  return [state, actions];
}

/**
 * 用于处理分页数据的Hook
 */
export function usePaginatedAsyncState<T = any>(
  asyncFunction?: (page: number, pageSize: number, ...args: any[]) => Promise<{ data: T[]; total: number }>,
  options: UseAsyncStateOptions & { initialPage?: number; initialPageSize?: number } = {}
) {
  const { initialPage = 1, initialPageSize = 10, ...restOptions } = options;
  
  const [pagination, setPagination] = useState({
    current: initialPage,
    pageSize: initialPageSize,
    total: 0
  });

  const [state, actions] = useAsyncState(asyncFunction, restOptions);

  const loadPage = useCallback(
    async (page: number = pagination.current, pageSize: number = pagination.pageSize, ...args: any[]) => {
      if (!asyncFunction) return;

      try {
        const result = await actions.execute(page, pageSize, ...args);
        
        setPagination(prev => ({
          ...prev,
          current: page,
          pageSize,
          total: result.total
        }));

        return result;
      } catch (error) {
        throw error;
      }
    },
    [asyncFunction, actions, pagination.current, pagination.pageSize]
  );

  const nextPage = useCallback(() => {
    const nextPageNum = pagination.current + 1;
    const maxPage = Math.ceil(pagination.total / pagination.pageSize);
    
    if (nextPageNum <= maxPage) {
      return loadPage(nextPageNum);
    }
  }, [pagination, loadPage]);

  const prevPage = useCallback(() => {
    const prevPageNum = pagination.current - 1;
    
    if (prevPageNum >= 1) {
      return loadPage(prevPageNum);
    }
  }, [pagination, loadPage]);

  const changePage = useCallback((page: number, pageSize?: number) => {
    return loadPage(page, pageSize || pagination.pageSize);
  }, [loadPage, pagination.pageSize]);

  const resetPagination = useCallback(() => {
    setPagination({
      current: initialPage,
      pageSize: initialPageSize,
      total: 0
    });
    actions.reset();
  }, [initialPage, initialPageSize, actions]);

  return {
    ...state,
    pagination,
    loadPage,
    nextPage,
    prevPage,
    changePage,
    reset: resetPagination,
    setData: actions.setData,
    setError: actions.setError,
    setLoading: actions.setLoading
  };
}

export default useAsyncState;
