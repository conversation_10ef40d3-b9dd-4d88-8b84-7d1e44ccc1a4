import React, { useState, useEffect } from 'react';
import { Row, Col, Typography, Progress, List, Avatar, Badge } from 'antd';
import {
  BookOutlined,
  ShoppingCartOutlined,
  HeartOutlined,
  TrophyOutlined,
  FireOutlined,
  StarOutlined,
  GiftOutlined,
  BellOutlined,
  MessageOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { StatCard, Card, Button, AnimatedWrapper } from '../ui';
import { theme } from '../../styles/theme';

const { Title, Text } = Typography;

interface DashboardData {
  stats: {
    booksOwned: number;
    booksSold: number;
    totalEarnings: number;
    favoriteBooks: number;
    readingGoal: number;
    readingProgress: number;
  };
  recentActivities: Activity[];
  recommendations: Book[];
  achievements: Achievement[];
  notifications: Notification[];
}

interface Activity {
  id: string;
  type: 'purchase' | 'sale' | 'review' | 'favorite';
  title: string;
  description: string;
  time: string;
  icon: React.ReactNode;
  color: string;
}

interface Book {
  id: string;
  title: string;
  author: string;
  cover: string;
  rating: number;
  price: number;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  total: number;
  unlocked: boolean;
  reward?: string;
}

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  time: string;
  read: boolean;
}

const DashboardContainer = styled.div`
  padding: ${theme.spacing[6]};
  max-width: 1400px;
  margin: 0 auto;
  
  .dashboard-header {
    margin-bottom: ${theme.spacing[8]};
    
    .welcome-section {
      background: ${theme.colors.gradients.primary};
      border-radius: ${theme.borderRadius['2xl']};
      padding: ${theme.spacing[8]};
      color: white;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: url('/api/placeholder/200/200') no-repeat;
        background-size: contain;
        opacity: 0.1;
      }
      
      .welcome-title {
        font-size: ${theme.typography.fontSize['3xl']};
        font-weight: ${theme.typography.fontWeight.bold};
        margin-bottom: ${theme.spacing[2]};
        color: white;
      }
      
      .welcome-subtitle {
        font-size: ${theme.typography.fontSize.lg};
        opacity: 0.9;
        margin-bottom: ${theme.spacing[6]};
      }
      
      .quick-actions {
        display: flex;
        gap: ${theme.spacing[4]};
        flex-wrap: wrap;
        
        .quick-action-btn {
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          backdrop-filter: blur(10px);
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
          }
        }
      }
    }
  }
  
  .stats-section {
    margin-bottom: ${theme.spacing[8]};
  }
  
  .content-sections {
    .section {
      margin-bottom: ${theme.spacing[8]};
      
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: ${theme.spacing[6]};
        
        .section-title {
          display: flex;
          align-items: center;
          gap: ${theme.spacing[3]};
          
          .section-icon {
            font-size: ${theme.typography.fontSize.xl};
            color: ${theme.colors.primary[500]};
          }
          
          h3 {
            margin: 0;
            font-size: ${theme.typography.fontSize.xl};
            font-weight: ${theme.typography.fontWeight.bold};
            color: ${theme.colors.gray[800]};
          }
        }
        
        .section-action {
          color: ${theme.colors.primary[500]};
          font-weight: ${theme.typography.fontWeight.medium};
        }
      }
    }
  }
  
  .activity-item {
    padding: ${theme.spacing[4]};
    border-radius: ${theme.borderRadius.lg};
    transition: all ${theme.animation.duration.fast};
    
    &:hover {
      background: ${theme.colors.gray[50]};
    }
    
    .activity-content {
      display: flex;
      align-items: center;
      gap: ${theme.spacing[4]};
      
      .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: ${theme.borderRadius.full};
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: ${theme.typography.fontSize.lg};
        color: white;
      }
      
      .activity-info {
        flex: 1;
        
        .activity-title {
          font-weight: ${theme.typography.fontWeight.semibold};
          color: ${theme.colors.gray[800]};
          margin-bottom: ${theme.spacing[1]};
        }
        
        .activity-description {
          color: ${theme.colors.gray[600]};
          font-size: ${theme.typography.fontSize.sm};
        }
      }
      
      .activity-time {
        color: ${theme.colors.gray[400]};
        font-size: ${theme.typography.fontSize.xs};
      }
    }
  }
  
  .achievement-card {
    padding: ${theme.spacing[4]};
    border-radius: ${theme.borderRadius.lg};
    border: 1px solid ${theme.colors.gray[200]};
    transition: all ${theme.animation.duration.fast};
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${theme.boxShadow.lg};
    }
    
    &.unlocked {
      background: ${theme.colors.gradients.primary};
      color: white;
      border-color: transparent;
    }
    
    .achievement-header {
      display: flex;
      align-items: center;
      gap: ${theme.spacing[3]};
      margin-bottom: ${theme.spacing[3]};
      
      .achievement-icon {
        font-size: ${theme.typography.fontSize['2xl']};
        color: ${theme.colors.warning[500]};
      }
      
      .achievement-title {
        font-weight: ${theme.typography.fontWeight.bold};
        margin: 0;
      }
    }
    
    .achievement-description {
      margin-bottom: ${theme.spacing[3]};
      opacity: 0.8;
    }
    
    .achievement-progress {
      margin-bottom: ${theme.spacing[2]};
    }
    
    .achievement-reward {
      font-size: ${theme.typography.fontSize.sm};
      color: ${theme.colors.warning[600]};
      font-weight: ${theme.typography.fontWeight.medium};
    }
  }
  
  .notification-item {
    padding: ${theme.spacing[3]} ${theme.spacing[4]};
    border-radius: ${theme.borderRadius.md};
    margin-bottom: ${theme.spacing[2]};
    transition: all ${theme.animation.duration.fast};
    
    &:hover {
      background: ${theme.colors.gray[50]};
    }
    
    &.unread {
      background: ${theme.colors.primary[50]};
      border-left: 4px solid ${theme.colors.primary[500]};
    }
    
    .notification-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: ${theme.spacing[1]};
      
      .notification-title {
        font-weight: ${theme.typography.fontWeight.semibold};
        color: ${theme.colors.gray[800]};
      }
      
      .notification-time {
        color: ${theme.colors.gray[400]};
        font-size: ${theme.typography.fontSize.xs};
      }
    }
    
    .notification-message {
      color: ${theme.colors.gray[600]};
      font-size: ${theme.typography.fontSize.sm};
    }
  }
`;

const UserDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: DashboardData = {
        stats: {
          booksOwned: 156,
          booksSold: 23,
          totalEarnings: 1280.50,
          favoriteBooks: 45,
          readingGoal: 50,
          readingProgress: 32
        },
        recentActivities: [
          {
            id: '1',
            type: 'purchase',
            title: '购买了《深度学习》',
            description: '成功购买了Ian Goodfellow的经典著作',
            time: '2小时前',
            icon: <ShoppingCartOutlined />,
            color: theme.colors.success[500]
          },
          {
            id: '2',
            type: 'sale',
            title: '出售了《算法导论》',
            description: '您的图书已被用户张三购买',
            time: '1天前',
            icon: <BookOutlined />,
            color: theme.colors.primary[500]
          }
        ],
        recommendations: [],
        achievements: [
          {
            id: '1',
            title: '阅读达人',
            description: '完成年度阅读目标',
            icon: <BookOutlined />,
            progress: 32,
            total: 50,
            unlocked: false,
            reward: '获得专属徽章'
          },
          {
            id: '2',
            title: '交易专家',
            description: '成功完成100笔交易',
            icon: <TrophyOutlined />,
            progress: 23,
            total: 100,
            unlocked: false,
            reward: '获得VIP特权'
          }
        ],
        notifications: [
          {
            id: '1',
            title: '新书推荐',
            message: '根据您的阅读偏好，为您推荐了5本新书',
            type: 'info',
            time: '1小时前',
            read: false
          },
          {
            id: '2',
            title: '订单更新',
            message: '您的订单已发货，预计明天到达',
            type: 'success',
            time: '3小时前',
            read: true
          }
        ]
      };
      
      setDashboardData(mockData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading || !dashboardData) {
    return <div>Loading...</div>;
  }

  const { stats, recentActivities, achievements, notifications } = dashboardData;

  return (
    <DashboardContainer>
      <AnimatedWrapper animation="fadeIn" duration="0.8s">
        <div className="dashboard-header">
          <div className="welcome-section">
            <h1 className="welcome-title">欢迎回来，书友！</h1>
            <p className="welcome-subtitle">
              今天是阅读的好日子，继续您的知识之旅吧
            </p>
            <div className="quick-actions">
              <Button className="quick-action-btn" rounded>
                <BookOutlined /> 浏览图书
              </Button>
              <Button className="quick-action-btn" rounded>
                <ShoppingCartOutlined /> 我的订单
              </Button>
              <Button className="quick-action-btn" rounded>
                <HeartOutlined /> 我的收藏
              </Button>
            </div>
          </div>
        </div>
      </AnimatedWrapper>

      <div className="stats-section">
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} lg={6}>
            <AnimatedWrapper animation="slideInUp" delay="0.1s">
              <StatCard
                title="拥有图书"
                value={stats.booksOwned}
                icon={<BookOutlined />}
                variant="gradient"
                gradient="primary"
                hoverable
              />
            </AnimatedWrapper>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <AnimatedWrapper animation="slideInUp" delay="0.2s">
              <StatCard
                title="已售图书"
                value={stats.booksSold}
                icon={<ShoppingCartOutlined />}
                color={theme.colors.success[500]}
                hoverable
              />
            </AnimatedWrapper>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <AnimatedWrapper animation="slideInUp" delay="0.3s">
              <StatCard
                title="总收益"
                value={`¥${stats.totalEarnings}`}
                icon={<GiftOutlined />}
                color={theme.colors.warning[500]}
                hoverable
              />
            </AnimatedWrapper>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <AnimatedWrapper animation="slideInUp" delay="0.4s">
              <StatCard
                title="阅读进度"
                value={`${stats.readingProgress}/${stats.readingGoal}`}
                progress={(stats.readingProgress / stats.readingGoal) * 100}
                icon={<StarOutlined />}
                color={theme.colors.primary[500]}
                hoverable
              />
            </AnimatedWrapper>
          </Col>
        </Row>
      </div>

      <div className="content-sections">
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <div className="section">
              <div className="section-header">
                <div className="section-title">
                  <CalendarOutlined className="section-icon" />
                  <h3>最近活动</h3>
                </div>
                <Button variant="link" className="section-action">
                  查看全部
                </Button>
              </div>
              
              <Card>
                <List
                  dataSource={recentActivities}
                  renderItem={(activity) => (
                    <div className="activity-item">
                      <div className="activity-content">
                        <div 
                          className="activity-icon"
                          style={{ backgroundColor: activity.color }}
                        >
                          {activity.icon}
                        </div>
                        <div className="activity-info">
                          <div className="activity-title">{activity.title}</div>
                          <div className="activity-description">{activity.description}</div>
                        </div>
                        <div className="activity-time">{activity.time}</div>
                      </div>
                    </div>
                  )}
                />
              </Card>
            </div>

            <div className="section">
              <div className="section-header">
                <div className="section-title">
                  <TrophyOutlined className="section-icon" />
                  <h3>成就系统</h3>
                </div>
              </div>
              
              <Row gutter={[16, 16]}>
                {achievements.map((achievement) => (
                  <Col key={achievement.id} xs={24} md={12}>
                    <div className={`achievement-card ${achievement.unlocked ? 'unlocked' : ''}`}>
                      <div className="achievement-header">
                        <span className="achievement-icon">{achievement.icon}</span>
                        <h4 className="achievement-title">{achievement.title}</h4>
                      </div>
                      <p className="achievement-description">{achievement.description}</p>
                      <Progress
                        percent={(achievement.progress / achievement.total) * 100}
                        format={() => `${achievement.progress}/${achievement.total}`}
                        className="achievement-progress"
                      />
                      {achievement.reward && (
                        <div className="achievement-reward">🎁 {achievement.reward}</div>
                      )}
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          </Col>

          <Col xs={24} lg={8}>
            <div className="section">
              <div className="section-header">
                <div className="section-title">
                  <BellOutlined className="section-icon" />
                  <h3>通知中心</h3>
                </div>
                <Button variant="link" className="section-action">
                  全部已读
                </Button>
              </div>
              
              <Card>
                {notifications.map((notification) => (
                  <div 
                    key={notification.id}
                    className={`notification-item ${!notification.read ? 'unread' : ''}`}
                  >
                    <div className="notification-header">
                      <span className="notification-title">{notification.title}</span>
                      <span className="notification-time">{notification.time}</span>
                    </div>
                    <div className="notification-message">{notification.message}</div>
                  </div>
                ))}
              </Card>
            </div>
          </Col>
        </Row>
      </div>
    </DashboardContainer>
  );
};

export default UserDashboard;
