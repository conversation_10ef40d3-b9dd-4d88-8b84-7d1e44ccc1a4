import React, { useState, useRef, useEffect } from 'react';
import { Input, AutoComplete, Tag, Tooltip, message } from 'antd';
import {
  SearchOutlined,
  RobotOutlined,
  PhoneOutlined,
  CameraOutlined,
  BulbOutlined,
  HistoryOutlined,
  FireOutlined,
  AudioOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { Button, AnimatedWrapper } from '../ui';
import { theme } from '../../styles/theme';

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'book' | 'author' | 'category' | 'ai-suggestion';
  confidence?: number;
  metadata?: any;
}

interface AIBookSearchProps {
  onSearch: (query: string, filters?: any) => void;
  placeholder?: string;
  showAIFeatures?: boolean;
  className?: string;
}

const SearchContainer = styled.div`
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  
  .search-wrapper {
    position: relative;
    background: white;
    border-radius: ${theme.borderRadius['2xl']};
    box-shadow: ${theme.boxShadow.xl};
    border: 2px solid transparent;
    transition: all ${theme.animation.duration.base};
    
    &:hover {
      box-shadow: ${theme.boxShadow['2xl']};
      border-color: ${theme.colors.primary[200]};
    }
    
    &.focused {
      border-color: ${theme.colors.primary[500]};
      box-shadow: 0 0 0 4px ${theme.colors.primary[100]};
    }
  }
  
  .search-input-wrapper {
    display: flex;
    align-items: center;
    padding: ${theme.spacing[4]} ${theme.spacing[6]};
    gap: ${theme.spacing[4]};
    
    .search-icon {
      font-size: ${theme.typography.fontSize.xl};
      color: ${theme.colors.primary[500]};
    }
    
    .ant-input {
      border: none;
      outline: none;
      box-shadow: none;
      font-size: ${theme.typography.fontSize.lg};
      background: transparent;
      
      &::placeholder {
        color: ${theme.colors.gray[400]};
      }
    }
    
    .search-actions {
      display: flex;
      align-items: center;
      gap: ${theme.spacing[2]};
      
      .action-btn {
        width: 40px;
        height: 40px;
        border-radius: ${theme.borderRadius.full};
        border: none;
        background: ${theme.colors.gray[100]};
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all ${theme.animation.duration.fast};
        
        &:hover {
          background: ${theme.colors.primary[100]};
          color: ${theme.colors.primary[600]};
          transform: scale(1.05);
        }
        
        &.active {
          background: ${theme.colors.primary[500]};
          color: white;
        }
      }
    }
  }
  
  .ai-suggestions {
    padding: ${theme.spacing[4]} ${theme.spacing[6]};
    border-top: 1px solid ${theme.colors.gray[200]};
    background: ${theme.colors.gray[50]};
    border-radius: 0 0 ${theme.borderRadius['2xl']} ${theme.borderRadius['2xl']};
    
    .suggestions-header {
      display: flex;
      align-items: center;
      gap: ${theme.spacing[2]};
      margin-bottom: ${theme.spacing[3]};
      
      .ai-icon {
        color: ${theme.colors.primary[500]};
      }
      
      .suggestions-title {
        font-size: ${theme.typography.fontSize.sm};
        font-weight: ${theme.typography.fontWeight.medium};
        color: ${theme.colors.gray[700]};
      }
    }
    
    .suggestions-list {
      display: flex;
      flex-wrap: wrap;
      gap: ${theme.spacing[2]};
      
      .suggestion-tag {
        cursor: pointer;
        transition: all ${theme.animation.duration.fast};
        border-radius: ${theme.borderRadius.full};
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: ${theme.boxShadow.md};
        }
        
        &.ai-suggestion {
          background: ${theme.colors.gradients.primary};
          color: white;
          border: none;
          
          .anticon {
            color: white;
          }
        }
        
        &.trending {
          background: ${theme.colors.gradients.sunset};
          color: white;
          border: none;
        }
      }
    }
  }
  
  .search-history {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: ${theme.borderRadius.xl};
    box-shadow: ${theme.boxShadow.xl};
    border: 1px solid ${theme.colors.gray[200]};
    margin-top: ${theme.spacing[2]};
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    
    .history-item {
      padding: ${theme.spacing[3]} ${theme.spacing[4]};
      cursor: pointer;
      transition: background-color ${theme.animation.duration.fast};
      display: flex;
      align-items: center;
      gap: ${theme.spacing[3]};
      
      &:hover {
        background: ${theme.colors.gray[50]};
      }
      
      .history-icon {
        color: ${theme.colors.gray[400]};
      }
      
      .history-text {
        flex: 1;
        color: ${theme.colors.gray[700]};
      }
      
      .history-time {
        color: ${theme.colors.gray[400]};
        font-size: ${theme.typography.fontSize.xs};
      }
    }
  }
`;

const VoiceSearchModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  
  .voice-content {
    background: white;
    border-radius: ${theme.borderRadius['2xl']};
    padding: ${theme.spacing[8]};
    text-align: center;
    max-width: 400px;
    
    .voice-icon {
      font-size: ${theme.typography.fontSize['4xl']};
      color: ${theme.colors.primary[500]};
      margin-bottom: ${theme.spacing[4]};
      animation: pulse 2s infinite;
    }
    
    .voice-text {
      font-size: ${theme.typography.fontSize.lg};
      color: ${theme.colors.gray[700]};
      margin-bottom: ${theme.spacing[6]};
    }
    
    .voice-result {
      background: ${theme.colors.gray[50]};
      padding: ${theme.spacing[4]};
      border-radius: ${theme.borderRadius.lg};
      margin-bottom: ${theme.spacing[4]};
      font-style: italic;
      color: ${theme.colors.gray[600]};
    }
  }
  
  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }
`;

const AIBookSearch: React.FC<AIBookSearchProps> = ({
  onSearch,
  placeholder = "搜索图书、作者或ISBN...",
  showAIFeatures = true,
  className
}) => {
  const [query, setQuery] = useState('');
  const [focused, setFocused] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [isVoiceSearching, setIsVoiceSearching] = useState(false);
  const [voiceResult, setVoiceResult] = useState('');
  const inputRef = useRef<any>(null);

  useEffect(() => {
    // 加载搜索历史
    const history = localStorage.getItem('search-history');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
    
    // 加载AI建议
    loadAISuggestions();
  }, []);

  const loadAISuggestions = async () => {
    // 模拟AI推荐
    const aiSuggestions: SearchSuggestion[] = [
      {
        id: '1',
        text: '人工智能入门书籍',
        type: 'ai-suggestion',
        confidence: 0.95
      },
      {
        id: '2',
        text: '编程类畅销书',
        type: 'ai-suggestion',
        confidence: 0.88
      },
      {
        id: '3',
        text: '文学经典推荐',
        type: 'ai-suggestion',
        confidence: 0.82
      },
      {
        id: '4',
        text: '最新科技图书',
        type: 'ai-suggestion',
        confidence: 0.79
      }
    ];
    
    setSuggestions(aiSuggestions);
  };

  const handleSearch = (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;
    
    // 添加到搜索历史
    const newHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 10);
    setSearchHistory(newHistory);
    localStorage.setItem('search-history', JSON.stringify(newHistory));
    
    // 执行搜索
    onSearch(searchQuery);
    setShowHistory(false);
    setFocused(false);
  };

  const handleVoiceSearch = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      message.error('您的浏览器不支持语音搜索功能');
      return;
    }

    setIsVoiceSearching(true);
    setVoiceResult('');

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.lang = 'zh-CN';
    recognition.continuous = false;
    recognition.interimResults = false;

    recognition.onresult = (event: any) => {
      const result = event.results[0][0].transcript;
      setVoiceResult(result);
      setQuery(result);
      
      setTimeout(() => {
        setIsVoiceSearching(false);
        handleSearch(result);
      }, 1000);
    };

    recognition.onerror = () => {
      message.error('语音识别失败，请重试');
      setIsVoiceSearching(false);
    };

    recognition.onend = () => {
      if (!voiceResult) {
        setIsVoiceSearching(false);
      }
    };

    recognition.start();
  };

  const handleImageSearch = () => {
    message.info('图像搜索功能即将上线，敬请期待！');
  };

  const handleAIAssist = () => {
    message.info('AI智能助手功能即将上线，敬请期待！');
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    handleSearch(suggestion.text);
  };

  const handleHistoryClick = (historyItem: string) => {
    setQuery(historyItem);
    handleSearch(historyItem);
  };

  return (
    <SearchContainer className={className}>
      <AnimatedWrapper animation="slideInDown" duration="0.6s">
        <div className={`search-wrapper ${focused ? 'focused' : ''}`}>
          <div className="search-input-wrapper">
            <SearchOutlined className="search-icon" />
            
            <Input
              ref={inputRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onFocus={() => {
                setFocused(true);
                setShowHistory(true);
              }}
              onBlur={() => {
                setTimeout(() => {
                  setFocused(false);
                  setShowHistory(false);
                }, 200);
              }}
              onPressEnter={() => handleSearch()}
              placeholder={placeholder}
              size="large"
            />
            
            {showAIFeatures && (
              <div className="search-actions">
                <Tooltip title="语音搜索">
                  <button className="action-btn" onClick={handleVoiceSearch}>
                    <AudioOutlined />
                  </button>
                </Tooltip>
                
                <Tooltip title="图像搜索">
                  <button className="action-btn" onClick={handleImageSearch}>
                    <CameraOutlined />
                  </button>
                </Tooltip>
                
                <Tooltip title="AI助手">
                  <button className="action-btn" onClick={handleAIAssist}>
                    <RobotOutlined />
                  </button>
                </Tooltip>
              </div>
            )}
          </div>
          
          {showAIFeatures && suggestions.length > 0 && (
            <div className="ai-suggestions">
              <div className="suggestions-header">
                <BulbOutlined className="ai-icon" />
                <span className="suggestions-title">AI智能推荐</span>
              </div>
              
              <div className="suggestions-list">
                {suggestions.map((suggestion) => (
                  <Tag
                    key={suggestion.id}
                    className={`suggestion-tag ${suggestion.type === 'ai-suggestion' ? 'ai-suggestion' : ''}`}
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    {suggestion.type === 'ai-suggestion' && <RobotOutlined />}
                    {suggestion.text}
                  </Tag>
                ))}
                
                <Tag className="suggestion-tag trending" onClick={() => handleSearch('热门图书')}>
                  <FireOutlined /> 热门图书
                </Tag>
              </div>
            </div>
          )}
        </div>
      </AnimatedWrapper>
      
      {showHistory && searchHistory.length > 0 && (
        <AnimatedWrapper animation="fadeIn" duration="0.3s">
          <div className="search-history">
            {searchHistory.map((item, index) => (
              <div
                key={index}
                className="history-item"
                onClick={() => handleHistoryClick(item)}
              >
                <HistoryOutlined className="history-icon" />
                <span className="history-text">{item}</span>
                <span className="history-time">最近搜索</span>
              </div>
            ))}
          </div>
        </AnimatedWrapper>
      )}
      
      {isVoiceSearching && (
        <VoiceSearchModal>
          <div className="voice-content">
            <AudioOutlined className="voice-icon" />
            <div className="voice-text">
              {voiceResult ? '识别完成' : '请说出您要搜索的内容...'}
            </div>
            {voiceResult && (
              <div className="voice-result">"{voiceResult}"</div>
            )}
            <Button
              variant="ghost"
              onClick={() => setIsVoiceSearching(false)}
            >
              取消
            </Button>
          </div>
        </VoiceSearchModal>
      )}
    </SearchContainer>
  );
};

export default AIBookSearch;
