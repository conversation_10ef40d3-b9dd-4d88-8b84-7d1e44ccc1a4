import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider } from 'styled-components';
import Button from '../Button';
import { theme } from '../../../styles/theme';

// 测试包装器
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('Button Component', () => {
  // 基础渲染测试
  describe('Basic Rendering', () => {
    it('renders button with text', () => {
      render(
        <TestWrapper>
          <Button>Test Button</Button>
        </TestWrapper>
      );
      
      expect(screen.getByRole('button', { name: /test button/i })).toBeInTheDocument();
    });

    it('renders button with icon', () => {
      const TestIcon = () => <span data-testid="test-icon">Icon</span>;
      
      render(
        <TestWrapper>
          <Button icon={<TestIcon />}>Button with Icon</Button>
        </TestWrapper>
      );
      
      expect(screen.getByTestId('test-icon')).toBeInTheDocument();
      expect(screen.getByText('Button with Icon')).toBeInTheDocument();
    });
  });

  // 变体测试
  describe('Variants', () => {
    const variants = ['primary', 'secondary', 'success', 'warning', 'error', 'ghost', 'link'] as const;
    
    variants.forEach(variant => {
      it(`renders ${variant} variant correctly`, () => {
        render(
          <TestWrapper>
            <Button variant={variant} data-testid={`button-${variant}`}>
              {variant} Button
            </Button>
          </TestWrapper>
        );
        
        const button = screen.getByTestId(`button-${variant}`);
        expect(button).toBeInTheDocument();
        expect(button).toHaveTextContent(`${variant} Button`);
      });
    });

    it('renders gradient variant correctly', () => {
      render(
        <TestWrapper>
          <Button variant="gradient" gradient="primary" data-testid="gradient-button">
            Gradient Button
          </Button>
        </TestWrapper>
      );
      
      expect(screen.getByTestId('gradient-button')).toBeInTheDocument();
    });
  });

  // 尺寸测试
  describe('Sizes', () => {
    const sizes = ['small', 'medium', 'large'] as const;
    
    sizes.forEach(size => {
      it(`renders ${size} size correctly`, () => {
        render(
          <TestWrapper>
            <Button size={size} data-testid={`button-${size}`}>
              {size} Button
            </Button>
          </TestWrapper>
        );
        
        expect(screen.getByTestId(`button-${size}`)).toBeInTheDocument();
      });
    });
  });

  // 状态测试
  describe('States', () => {
    it('renders disabled state correctly', () => {
      render(
        <TestWrapper>
          <Button disabled>Disabled Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('renders loading state correctly', () => {
      render(
        <TestWrapper>
          <Button loading>Loading Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      // 可以添加加载图标的测试
    });
  });

  // 交互测试
  describe('Interactions', () => {
    it('handles click events', () => {
      const handleClick = jest.fn();
      
      render(
        <TestWrapper>
          <Button onClick={handleClick}>Clickable Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('does not trigger click when disabled', () => {
      const handleClick = jest.fn();
      
      render(
        <TestWrapper>
          <Button onClick={handleClick} disabled>Disabled Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('does not trigger click when loading', () => {
      const handleClick = jest.fn();
      
      render(
        <TestWrapper>
          <Button onClick={handleClick} loading>Loading Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  // 属性测试
  describe('Props', () => {
    it('applies custom className', () => {
      render(
        <TestWrapper>
          <Button className="custom-class">Custom Class Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });

    it('applies custom styles', () => {
      const customStyle = { backgroundColor: 'red' };
      
      render(
        <TestWrapper>
          <Button style={customStyle}>Styled Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveStyle('background-color: red');
    });

    it('renders as full width when specified', () => {
      render(
        <TestWrapper>
          <Button fullWidth data-testid="full-width-button">Full Width Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByTestId('full-width-button');
      expect(button).toBeInTheDocument();
    });

    it('renders with rounded corners when specified', () => {
      render(
        <TestWrapper>
          <Button rounded data-testid="rounded-button">Rounded Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByTestId('rounded-button');
      expect(button).toBeInTheDocument();
    });

    it('renders with elevation when specified', () => {
      render(
        <TestWrapper>
          <Button elevated data-testid="elevated-button">Elevated Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByTestId('elevated-button');
      expect(button).toBeInTheDocument();
    });
  });

  // 可访问性测试
  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <Button aria-label="Accessible Button">Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Accessible Button');
    });

    it('supports keyboard navigation', () => {
      const handleClick = jest.fn();
      
      render(
        <TestWrapper>
          <Button onClick={handleClick}>Keyboard Button</Button>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button');
      button.focus();
      
      expect(button).toHaveFocus();
      
      fireEvent.keyDown(button, { key: 'Enter' });
      expect(handleClick).toHaveBeenCalledTimes(1);
      
      fireEvent.keyDown(button, { key: ' ' });
      expect(handleClick).toHaveBeenCalledTimes(2);
    });
  });
});
