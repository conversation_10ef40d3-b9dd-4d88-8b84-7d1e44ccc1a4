import React from 'react';
import { Form, Input, Select, DatePicker, InputNumber, Switch, Rate, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { theme } from '../../styles/theme';

export type FieldType = 
  | 'text' 
  | 'password' 
  | 'email' 
  | 'number' 
  | 'textarea' 
  | 'select' 
  | 'date' 
  | 'switch' 
  | 'rate' 
  | 'upload';

interface Option {
  label: string;
  value: string | number;
  disabled?: boolean;
}

interface FormFieldProps {
  name: string;
  label: string;
  type?: FieldType;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: Option[];
  rules?: any[];
  tooltip?: string;
  extra?: string;
  size?: 'small' | 'middle' | 'large';
  rows?: number;
  min?: number;
  max?: number;
  step?: number;
  format?: string;
  multiple?: boolean;
  showCount?: boolean;
  maxLength?: number;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  addonBefore?: React.ReactNode;
  addonAfter?: React.ReactNode;
  allowClear?: boolean;
  showSearch?: boolean;
  filterOption?: boolean | ((input: string, option: any) => boolean);
  onChange?: (value: any) => void;
  onBlur?: (e: any) => void;
  onFocus?: (e: any) => void;
  style?: React.CSSProperties;
  className?: string;
}

const StyledFormItem = styled(Form.Item)`
  .ant-form-item-label {
    > label {
      font-weight: ${theme.typography.fontWeight.semibold};
      color: ${theme.colors.gray[700]};
      
      &.ant-form-item-required {
        &::before {
          color: ${theme.colors.error[500]};
        }
      }
    }
  }
  
  .ant-form-item-control {
    .ant-input,
    .ant-input-number,
    .ant-select-selector,
    .ant-picker {
      border-radius: ${theme.borderRadius.md};
      border-color: ${theme.colors.gray[300]};
      transition: all ${theme.animation.duration.fast};
      
      &:hover {
        border-color: ${theme.colors.primary[400]};
      }
      
      &:focus,
      &.ant-input-focused,
      &.ant-select-focused,
      &.ant-picker-focused {
        border-color: ${theme.colors.primary[500]};
        box-shadow: 0 0 0 2px ${theme.colors.primary[100]};
      }
    }
    
    .ant-input-affix-wrapper {
      border-radius: ${theme.borderRadius.md};
      border-color: ${theme.colors.gray[300]};
      
      &:hover {
        border-color: ${theme.colors.primary[400]};
      }
      
      &.ant-input-affix-wrapper-focused {
        border-color: ${theme.colors.primary[500]};
        box-shadow: 0 0 0 2px ${theme.colors.primary[100]};
      }
    }
    
    .ant-switch {
      &.ant-switch-checked {
        background-color: ${theme.colors.primary[500]};
      }
    }
    
    .ant-rate {
      color: ${theme.colors.warning[400]};
      
      .ant-rate-star.ant-rate-star-full {
        color: ${theme.colors.warning[500]};
      }
    }
    
    .ant-upload {
      .ant-upload-btn {
        border-radius: ${theme.borderRadius.md};
        border-color: ${theme.colors.gray[300]};
        transition: all ${theme.animation.duration.fast};
        
        &:hover {
          border-color: ${theme.colors.primary[400]};
        }
      }
    }
  }
  
  .ant-form-item-explain-error {
    color: ${theme.colors.error[500]};
    font-size: ${theme.typography.fontSize.sm};
  }
  
  .ant-form-item-extra {
    color: ${theme.colors.gray[500]};
    font-size: ${theme.typography.fontSize.sm};
  }
`;

const FormField: React.FC<FormFieldProps> = ({
  name,
  label,
  type = 'text',
  placeholder,
  required = false,
  disabled = false,
  options = [],
  rules = [],
  tooltip,
  extra,
  size = 'middle',
  rows = 4,
  min,
  max,
  step,
  format = 'YYYY-MM-DD',
  multiple = false,
  showCount = false,
  maxLength,
  prefix,
  suffix,
  addonBefore,
  addonAfter,
  allowClear = true,
  showSearch = false,
  filterOption = true,
  onChange,
  onBlur,
  onFocus,
  style,
  className
}) => {
  // 构建验证规则
  const fieldRules = [
    ...(required ? [{ required: true, message: `请输入${label}` }] : []),
    ...rules
  ];

  // 根据类型添加特定规则
  if (type === 'email') {
    fieldRules.push({
      type: 'email',
      message: '请输入有效的邮箱地址'
    });
  }

  const renderField = () => {
    const commonProps = {
      placeholder: placeholder || `请输入${label}`,
      disabled,
      size,
      onChange,
      onBlur,
      onFocus,
      style,
      className
    };

    switch (type) {
      case 'password':
        return (
          <Input.Password
            {...commonProps}
            prefix={prefix}
            suffix={suffix}
            addonBefore={addonBefore}
            addonAfter={addonAfter}
            allowClear={allowClear}
            maxLength={maxLength}
            showCount={showCount}
          />
        );

      case 'email':
        return (
          <Input
            {...commonProps}
            type="email"
            prefix={prefix}
            suffix={suffix}
            addonBefore={addonBefore}
            addonAfter={addonAfter}
            allowClear={allowClear}
            maxLength={maxLength}
            showCount={showCount}
          />
        );

      case 'number':
        return (
          <InputNumber
            {...commonProps}
            min={min}
            max={max}
            step={step}
            style={{ width: '100%', ...style }}
          />
        );

      case 'textarea':
        return (
          <Input.TextArea
            {...commonProps}
            rows={rows}
            allowClear={allowClear}
            maxLength={maxLength}
            showCount={showCount}
          />
        );

      case 'select':
        return (
          <Select
            {...commonProps}
            placeholder={placeholder || `请选择${label}`}
            options={options}
            allowClear={allowClear}
            showSearch={showSearch}
            filterOption={filterOption}
            mode={multiple ? 'multiple' : undefined}
          />
        );

      case 'date':
        return (
          <DatePicker
            {...commonProps}
            placeholder={placeholder || `请选择${label}`}
            format={format}
            allowClear={allowClear}
            style={{ width: '100%', ...style }}
          />
        );

      case 'switch':
        return (
          <Switch
            {...commonProps}
            checkedChildren="是"
            unCheckedChildren="否"
          />
        );

      case 'rate':
        return (
          <Rate
            {...commonProps}
            allowHalf
            allowClear={allowClear}
          />
        );

      case 'upload':
        return (
          <Upload
            {...commonProps}
            multiple={multiple}
          >
            <div style={{ 
              padding: '20px', 
              textAlign: 'center', 
              border: `1px dashed ${theme.colors.gray[300]}`,
              borderRadius: theme.borderRadius.md,
              cursor: 'pointer'
            }}>
              <UploadOutlined style={{ fontSize: '24px', color: theme.colors.gray[400] }} />
              <div style={{ marginTop: '8px', color: theme.colors.gray[600] }}>
                {placeholder || '点击或拖拽文件到此区域上传'}
              </div>
            </div>
          </Upload>
        );

      default:
        return (
          <Input
            {...commonProps}
            prefix={prefix}
            suffix={suffix}
            addonBefore={addonBefore}
            addonAfter={addonAfter}
            allowClear={allowClear}
            maxLength={maxLength}
            showCount={showCount}
          />
        );
    }
  };

  return (
    <StyledFormItem
      name={name}
      label={label}
      rules={fieldRules}
      tooltip={tooltip}
      extra={extra}
      style={style}
      className={className}
    >
      {renderField()}
    </StyledFormItem>
  );
};

export default FormField;
